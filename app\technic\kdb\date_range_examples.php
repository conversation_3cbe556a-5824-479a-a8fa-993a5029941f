<?php
/**
 * Date Range Examples for Ticket Summary
 * This file shows how to use different date ranges
 */

// Include the main file functions (you can copy the date functions from get_summary_tickets_email.php)
require_once 'get_summary_tickets_email.php';

echo "<h2>Available Date Range Options</h2>";

// Example 1: Current Month (Default)
$currentMonth = getCurrentMonthRange();
echo "<h3>1. Current Month</h3>";
echo "Start: " . $currentMonth['start_date'] . "<br>";
echo "End: " . $currentMonth['end_date'] . "<br>";
echo "Description: " . $currentMonth['description'] . "<br><br>";

// Example 2: Previous Month
$previousMonth = getPreviousMonthRange();
echo "<h3>2. Previous Month</h3>";
echo "Start: " . $previousMonth['start_date'] . "<br>";
echo "End: " . $previousMonth['end_date'] . "<br>";
echo "Description: " . $previousMonth['description'] . "<br><br>";

// Example 3: Current Year to Date
$currentYear = getCurrentYearRange();
echo "<h3>3. Current Year to Date</h3>";
echo "Start: " . $currentYear['start_date'] . "<br>";
echo "End: " . $currentYear['end_date'] . "<br>";
echo "Description: " . $currentYear['description'] . "<br><br>";

// Example 4: Last 30 Days
$last30Days = getLast30DaysRange();
echo "<h3>4. Last 30 Days</h3>";
echo "Start: " . $last30Days['start_date'] . "<br>";
echo "End: " . $last30Days['end_date'] . "<br>";
echo "Description: " . $last30Days['description'] . "<br><br>";

// Example 5: Last 7 Days
$last7Days = getLast7DaysRange();
echo "<h3>5. Last 7 Days</h3>";
echo "Start: " . $last7Days['start_date'] . "<br>";
echo "End: " . $last7Days['end_date'] . "<br>";
echo "Description: " . $last7Days['description'] . "<br><br>";

// Example 6: Current Quarter
$currentQuarter = getCurrentQuarterRange();
echo "<h3>6. Current Quarter</h3>";
echo "Start: " . $currentQuarter['start_date'] . "<br>";
echo "End: " . $currentQuarter['end_date'] . "<br>";
echo "Description: " . $currentQuarter['description'] . "<br><br>";

// Example 7: Custom Range
$customRange = getCustomRange('2025-06-01', '2025-06-15');
echo "<h3>7. Custom Range</h3>";
echo "Start: " . $customRange['start_date'] . "<br>";
echo "End: " . $customRange['end_date'] . "<br>";
echo "Description: " . $customRange['description'] . "<br><br>";

echo "<hr>";
echo "<h3>How to Use in get_summary_tickets_email.php:</h3>";
echo "<pre>";
echo "// Change this line in get_summary_tickets_email.php:\n";
echo "\n";
echo "// For current month (default):\n";
echo "\$dateRange = getCurrentMonthRange();\n";
echo "\n";
echo "// For previous month:\n";
echo "\$dateRange = getPreviousMonthRange();\n";
echo "\n";
echo "// For last 30 days:\n";
echo "\$dateRange = getLast30DaysRange();\n";
echo "\n";
echo "// For current year:\n";
echo "\$dateRange = getCurrentYearRange();\n";
echo "\n";
echo "// For current quarter:\n";
echo "\$dateRange = getCurrentQuarterRange();\n";
echo "\n";
echo "// For custom range:\n";
echo "\$dateRange = getCustomRange('2025-06-01', '2025-06-30');\n";
echo "</pre>";

echo "<h3>URL Parameters (Optional Enhancement):</h3>";
echo "<p>You can also modify the script to accept URL parameters:</p>";
echo "<pre>";
echo "// Example URL: get_summary_tickets_email.php?range=previous_month\n";
echo "// Example URL: get_summary_tickets_email.php?start=2025-06-01&end=2025-06-30\n";
echo "\n";
echo "// Add this code to handle URL parameters:\n";
echo "if (isset(\$_GET['range'])) {\n";
echo "    switch (\$_GET['range']) {\n";
echo "        case 'previous_month':\n";
echo "            \$dateRange = getPreviousMonthRange();\n";
echo "            break;\n";
echo "        case 'last_30_days':\n";
echo "            \$dateRange = getLast30DaysRange();\n";
echo "            break;\n";
echo "        case 'current_year':\n";
echo "            \$dateRange = getCurrentYearRange();\n";
echo "            break;\n";
echo "        case 'current_quarter':\n";
echo "            \$dateRange = getCurrentQuarterRange();\n";
echo "            break;\n";
echo "        default:\n";
echo "            \$dateRange = getCurrentMonthRange();\n";
echo "    }\n";
echo "} elseif (isset(\$_GET['start']) && isset(\$_GET['end'])) {\n";
echo "    \$dateRange = getCustomRange(\$_GET['start'], \$_GET['end']);\n";
echo "} else {\n";
echo "    \$dateRange = getCurrentMonthRange();\n";
echo "}\n";
echo "</pre>";
?>

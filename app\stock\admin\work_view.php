<?php
/*
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
*/

include_once('../function/function.php');
include_once('../function/policyadmin.php');

$work_id = mysqli_real_escape_string($db, $_GET['work_id']);

$qryinv = "";
$qryinvb = "";
$qryinvbroken = "";

?>

<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>1-TO-ALL Stock | VIEW ONSITE</title>

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="../plugins/fontawesome-free/css/all.min.css">
  <!-- Ionicons -->
  <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
  <!-- Tempusdominus Bootstrap 4 -->
  <link rel="stylesheet" href="../plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css">
  <!-- iCheck -->
  <link rel="stylesheet" href="../plugins/icheck-bootstrap/icheck-bootstrap.min.css">
  <!-- JQVMap -->
  <link rel="stylesheet" href="../plugins/jqvmap/jqvmap.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="../dist/css/adminlte.min.css">
  <!-- Ekko Lightbox -->
  <link rel="stylesheet" href="../plugins/ekko-lightbox/ekko-lightbox.css">

  <!-- DataTables -->
  <link rel="stylesheet" href="../plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
  <link rel="stylesheet" href="../plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
  <link rel="stylesheet" href="../plugins/datatables-buttons/css/buttons.bootstrap4.min.css">

  <link rel="stylesheet" href="../js/sweetalert2.min.css">

  <!-- jQuery -->
  <script src="../plugins/jquery/jquery.min.js"></script>
  <!-- jQuery UI 1.11.4 -->
  <script src="../plugins/jquery-ui/jquery-ui.min.js"></script>

  <script src="../js/sweetalert2.min.js"></script>

  <!-- Resolve conflict in jQuery UI tooltip with Bootstrap tooltip -->

  <!-- Bootstrap 4 -->
  <script src="../plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
  <!-- DataTables  & Plugins -->
  <script src="../plugins/datatables/jquery.dataTables.min.js"></script>
  <script src="../plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
  <script src="../plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
  <script src="../plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
  <script src="../plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
  <script src="../plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
  <script src="../plugins/jszip/jszip.min.js"></script>
  <script src="../plugins/pdfmake/pdfmake.min.js"></script>
  <script src="../plugins/pdfmake/vfs_fonts.js"></script>
  <script src="../plugins/datatables-buttons/js/buttons.html5.min.js"></script>
  <script src="../plugins/datatables-buttons/js/buttons.print.min.js"></script>
  <script src="../plugins/datatables-buttons/js/buttons.colVis.min.js"></script>

  <!-- Ekko Lightbox -->
  <script src="../plugins/ekko-lightbox/ekko-lightbox.min.js"></script>
  <!-- ChartJS -->
  <script src="../plugins/chart.js/Chart.min.js"></script>
  <!-- Sparkline -->
  <script src="../plugins/sparklines/sparkline.js"></script>
  <!-- JQVMap -->
  <script src="../plugins/jqvmap/jquery.vmap.min.js"></script>
  <script src="../plugins/jqvmap/maps/jquery.vmap.usa.js"></script>
  <!-- jQuery Knob Chart -->
  <script src="../plugins/jquery-knob/jquery.knob.min.js"></script>
  <!-- daterangepicker -->
  <script src="../plugins/moment/moment.min.js"></script>
  <script src="../plugins/daterangepicker/daterangepicker.js"></script>
  <!-- Tempusdominus Bootstrap 4 -->
  <script src="../plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js"></script>
  <!-- overlayScrollbars -->
  <script src="../plugins/overlayScrollbars/js/jquery.overlayScrollbars.min.js"></script>
  <!-- AdminLTE App -->
  <script src="../dist/js/adminlte.js"></script>
  <script type="text/javascript" src="../js/search.js"></script>
</head>

<body class="hold-transition sidebar-mini layout-navbar-fixed">
  <div class="wrapper">

    <!-- Preloader
  <div class="preloader flex-column justify-content-center align-items-center">
    <img class="animation__shake" src="dist/img/AdminLTELogo.png" alt="AdminLTELogo" height="60" width="60">
  </div> -->

    <!-- Navbar -->
    <?php include 'navbar.php'; ?>
    <!-- /.navbar -->

    <!-- Sidebar -->
    <?php include 'menu.php'; ?>
    <!-- /.Sidebar -->

    <!-- Content Wrapper. Contains page content -->
    <div class="content-wrapper">
      <!-- Content Header (Page header) -->
      <div class="content-header">
        <div class="container-fluid">
          <div class="row mb-2">
            <div class="col-sm-6">
              <h1 class="m-0">VIEW ONSITE</h1>
            </div><!-- /.col -->
            <div class="col-sm-6">
              <ol class="breadcrumb float-sm-right">
                <li class="breadcrumb-item"><a href="work.php">WORK ONSITE</a></li>
                <li class="breadcrumb-item"><a href="#">VIEW ONSITE</a></li>
              </ol>
            </div><!-- /.col -->
          </div><!-- /.row -->
        </div><!-- /.container-fluid -->
      </div>
      <!-- /.content-header -->


      <!-- Main content -->
      <section class="content">
        <div class="container-fluid">
          <!-- Small boxes (Stat box) -->
          <form method="post" action="#">
            <button type="button" onclick="location.href = 'work.php';" class="btn btn-secondary btn-lg"><i class="fa-solid fa-arrow-right-from-bracket fa-flip-horizontal"></i> BACK</button>
            <?php
            $swithbtn = mysqli_fetch_assoc(mysqli_query($db, "SELECT onsite_status FROM inventory_onsite_log WHERE onsite_log_id='$work_id'"));

            switch ($swithbtn['onsite_status']) {
              case 'INCustomer':
                echo '<button type="button" onclick="location.href = \'edit_work.php?work_id=' . $work_id . '\';" class="btn btn-warning btn-lg"><i class="fa-solid fa-pen-to-square"></i> EDIT</button>
          <button type="submit" name="clear" class="btn btn-success btn-lg"><i class="fa-solid fa-circle-check"></i> COMPLETE</button>
          <button type="submit" name="cancel" class="btn btn-lg btn-danger"><i class="fa-solid fa-ban"></i> CANCEL WORK</button>';
                break;

              default:
                echo '
          <button type="button" class="btn btn-warning btn-lg btn-rollback"><i class="fa fa-rotate-backward"></i>Rollback</button>
          <button hidden type="button" class="btn btn-success btn-lg" disabled><i class="fa-solid fa-circle-check"></i> COMPLETE</button>
          <button hidden type="submit" class="btn btn-lg btn-danger" disabled><i class="fa-solid fa-ban"></i> CANCEL WORK</button>';
                break;
            }
            ?>

          </form>
          <hr>
          <div class="row">
            <div class="col">
              <div class="card card-primary">

                <div class="card-body">
                  <?php

                  if (isset($_POST['clear'])) {
                    echo '
      <script>
      setTimeout(function() {
        Swal.fire({
          title: "ต้องการปิดงาน หรือไม่ ?",
          text: "หลังจากกดยืนยันแล้วจะไม่สามารถแก้ไขได้ !",
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#28a745",
          cancelButtonColor: "#3085d6",
          confirmButtonText: "Yes, COMPLETE it!"
        }).then((result) => {
          if (result.isConfirmed) {
            Swal.fire(
              "ปิดงานเรียบร้อย !",
              "Work COMPLETE แล้ว",
              "success"
            ).then(function(){
              window.location = "clear_work.php?work_id=' . $work_id . '";
            });
            
          }
        });
        }, 100);
        </script>';
                  }


                  if (isset($_POST['cancel'])) {

                    $qinv_id = mysqli_query($db, "SELECT * FROM inventory_onsite_log WHERE onsite_log_id='$work_id'");
                    $select_inv_id = mysqli_fetch_assoc($qinv_id);
                    $inv_id = $select_inv_id['inv_id'];

                    echo '
      <script>
      setTimeout(function() {
        Swal.fire({
          title: "ต้องการยกเลิก Work หรือไม่ ?",
          text: "หลังจากกดยืนยันแล้วจะไม่สามารถแก้ไขได้ !",
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#d33",
          cancelButtonColor: "#3085d6",
          confirmButtonText: "Yes, Cancel it!"
        }).then((result) => {
          if (result.isConfirmed) {
            Swal.fire(
              "ยกเลิก Work เรียบร้อย !",
              "Work ถูกยกเลิกแล้ว",
              "success"
            ).then(function(){
              window.location = "cancel_work.php?work_id=' . $work_id . '&inv_id=' . $inv_id . '";
            });
            
          }
        });
        }, 100);
        </script>';
                  }



                  $qryonsite_inv_id = mysqli_query($db, "SELECT * FROM inventory_onsite_log WHERE onsite_log_id='$work_id'");
                  $selectonsite_inv_id = mysqli_fetch_assoc($qryonsite_inv_id);
                  $INinv = fix_value_num($selectonsite_inv_id['inv_id']);
                  $INinvb = fix_value_num($selectonsite_inv_id['inv_idb']);
                  $INinvbroken = fix_value_num($selectonsite_inv_id['inv_id_broken']);
                  $INso = fix_value_num($selectonsite_inv_id['serviceorder']);
                  $onsiteid = fix_value_num($selectonsite_inv_id['onsite_id']);
                  $status = fix_value_num($selectonsite_inv_id['onsite_status']);


                  $sqlqryinvb = "SELECT inventory_data.* , inventory_type.type_name , inventory_brand.brand_name , inventory_model.model
                            FROM  (((inventory_data 
                            INNER JOIN inventory_type ON inventory_type.type_id = inventory_type_id)
                            INNER JOIN inventory_brand ON inventory_brand.brand_id = inventory_brand_id)
                            INNER JOIN inventory_model ON inventory_model.model_id = inventory_model_id) WHERE inv_id IN ($INinvb) ORDER BY inventory_model_id ASC";

                  try {
                    $qryinvb = @mysqli_query($db, $sqlqryinvb); // แสดงรายการอุปกรณ์ใน order ทั้งหมด
                  } catch (Exception $e) {
                    $qryinvb = "";
                  }

                  $ii = 1;
                  if ($INinvbroken == "") {
                    $INinvbroken = "0";
                  }

                  $sqlqryinvbroken = "SELECT inventory_data.* , inventory_type.type_name , inventory_brand.brand_name , inventory_model.model
                            FROM  (((inventory_data 
                            INNER JOIN inventory_type ON inventory_type.type_id = inventory_type_id)
                            INNER JOIN inventory_brand ON inventory_brand.brand_id = inventory_brand_id)
                            INNER JOIN inventory_model ON inventory_model.model_id = inventory_model_id) WHERE inv_id IN ($INinvbroken) ORDER BY inventory_model_id ASC";
                  try {
                    $qryinvbroken = @mysqli_query($db, $sqlqryinvbroken); // แสดงรายการอุปกรณ์ใน order ทั้งหมด
                  } catch (Exception $e) {
                    //$qryinvbroken = "";
                  }
                  $iii = 1;

                  echo '<h3>Information</h3>';
                  echo '<table class="table-sm table-striped table-hover table-bordered">
                              <tr><th>ID</th><td>' . $selectonsite_inv_id['onsite_log_id'] . '</td></tr>
                              <tr><th>STATUS</th><td>' . $selectonsite_inv_id['onsite_status'] . '</td></tr>
                              <tr><th>CREATE DATE</th><td>' . $selectonsite_inv_id['onsite_date'] . '</td></tr>
                              <tr><th>ONSITE BY</th><td>' . $selectonsite_inv_id['onsite_by'] . ' (' . $selectonsite_inv_id['onsite_companie'] . ')</td></tr>
                              <tr><th>WO#</th><td>' . $selectonsite_inv_id['work_id'] . '</td></tr>
                              <tr><th>ONSITE ID</th><td>' . $selectonsite_inv_id['onsite_id'] . '</td></tr>
                              </table><hr>';

                  echo '<h3>Install Equipment ';

                  switch ($status) {
                    case 'INCustomer':
                      echo '<button type="button" onclick="location.href = \'edit_work_ins.php?work_id=' . $work_id . '\';" class="btn btn-warning btn-xs"><i class="fa-solid fa-circle-plus"></i> <b>ADD</b></button>
                                <button type="button" onclick="location.href = \'edit_work_insdel.php?work_id=' . $work_id . '\';" class="btn btn-danger btn-xs"><i class="fa-solid fa-trash"></i> <b>REMOVE</b></button></h3>';
                      break;

                    default:
                      echo '</h3>';

                      break;
                  }

                  if ($INinv == "") {
                    $INinv = "0";
                  }

                  // เลือกอุปกรณ์ตาม order จากค่า id ของอุปกรณ์
                  $sqlqryinv = "SELECT inventory_data.* , inventory_type.type_name , inventory_brand.brand_name , inventory_model.model
                   FROM  (((inventory_data 
                   INNER JOIN inventory_type ON inventory_type.type_id = inventory_type_id)
                   INNER JOIN inventory_brand ON inventory_brand.brand_id = inventory_brand_id)
                   INNER JOIN inventory_model ON inventory_model.model_id = inventory_model_id) WHERE inv_id IN ($INinv) ORDER BY inventory_model_id ASC";

                  try {
                    $qryinv = @mysqli_query($db, $sqlqryinv); // แสดงรายการอุปกรณ์ใน order ทั้งหมด
                  } catch (Exception $e) {
                    //$qryinv = "";
                  }
                  $i = 1;

                  if (empty($qryinv)) {
                    echo 'ไม่มีอุปกรณ์ติดตั้งที่ Site ลูกค้า';
                  } else {
                    while ($row = mysqli_fetch_array($qryinv)) {
                      echo '<b>' . $i . '</b> - ' . $row["type_name"] . ' | ' . $row["brand_name"] . ' | ' . $row["model"] . ' | ' . $row["serials"] . '<br>';
                      $i++;
                    }
                  }

                  echo '<hr>';
                  echo '<h3>Recover Equipment ';

                  switch ($status) {
                    case 'INCustomer':
                      echo '<button type="button" onclick="location.href = \'edit_work_reco.php?work_id=' . $work_id . '\';" class="btn btn-warning btn-xs"><i class="fa-solid fa-circle-plus"></i> <b>ADD</b></button>
                                <button type="button" onclick="location.href = \'edit_work_recodel.php?work_id=' . $work_id . '\';" class="btn btn-danger btn-xs"><i class="fa-solid fa-trash"></i> <b>REMOVE</b></button></h3>';
                      break;

                    default:
                      echo '</h3>';
                      break;
                  }

                  if (empty($qryinvb) || $qryinvb == "") {
                    echo 'ไม่มีอุปกรณ์เก็บกลับจาก Site ลูกค้า';
                  } else {
                    while ($row = mysqli_fetch_array($qryinvb)) {

                      echo "<b>" . $ii . "</b> - " . $row["type_name"] . " | " . $row["brand_name"] . " | " . $row["model"] . " | " . $row["serials"] . "<br>";

                      $ii++;
                    }
                  }


                  echo '<hr>';
                  echo '<h3>Recover Equipment (ชำรุด/Terminate) ';

                  switch ($status) {
                    case 'INCustomer':
                      echo '<button type="button" onclick="location.href = \'edit_work_reco_broken.php?work_id=' . $work_id . '\';" class="btn btn-warning btn-xs"><i class="fa-solid fa-circle-plus"></i> <b>ADD</b></button>
                                <button type="button" onclick="location.href = \'edit_work_recodel_broken.php?work_id=' . $work_id . '\';" class="btn btn-danger btn-xs"><i class="fa-solid fa-trash"></i> <b>REMOVE</b></button></h3>';
                      break;

                    default:
                      echo '</h3>';
                      break;
                  }

                  if (empty($qryinvbroken)) {
                    echo 'ไม่มีอุปกรณ์ชำรุดเก็บกลับจาก Site ลูกค้า';
                  } else {
                    while ($row = mysqli_fetch_array($qryinvbroken)) {

                      echo "<b>" . $iii . "</b> - " . $row["type_name"] . " | " . $row["brand_name"] . " | " . $row["model"] . " | " . $row["serials"] . "<br>";

                      $iii++;
                    }
                  }

                  ?>
                  <hr>

                  <h3>Remark <button type="submit" name="edit_remark" class="btn btn-primary btn-xs" data-toggle="modal" data-target="#remark"><i class="fa-solid fa-pen-to-square"></i> EDIT</button></h3>

                  <!-- Modal -->
                  <form action="work_view.php?work_id=<?= $work_id ?>" method="POST">
                    <div class="modal fade" id="remark" tabindex="-1" aria-labelledby="remarkLabel" aria-hidden="true">
                      <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
                        <div class="modal-content">
                          <div class="modal-header">
                            <h5 class="modal-title" id="remarkLabel">Edit Remark</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                              <span aria-hidden="true">&times;</span>
                            </button>
                          </div>
                          <div class="modal-body">
                            <div class="form-group">
                              <input type="text" class="form-control" name="work_ids" value="<?= $work_id ?>" hidden>
                            </div>

                            <div class="form-group">

                              <?php

                              if (!empty($work_id)) {
                                // ดึงข้อมูลจากฐานข้อมูล
                                $stmt = $pdo->prepare("SELECT remark FROM inventory_onsite_log WHERE onsite_log_id = :work_id");
                                $stmt->bindParam(':work_id', $work_id);
                                $stmt->execute();
                                $result = $stmt->fetch(PDO::FETCH_ASSOC);

                                // ตรวจสอบว่าพบข้อมูล
                                if ($result && isset($result['remark']) && $result['remark'] !== null && $result['remark'] !== "") {
                                  echo '<textarea name="remark" class="form-control" rows="15">' . ($result['remark']) . '</textarea>'; // แสดง remark หากมีข้อมูล
                                } else {
                                  echo '<textarea name="remark" class="form-control" rows="15"></textarea>';
                                }
                              } else {
                                $work_id = "";
                                $remark = "ไม่มีข้อมูล";
                              }


                              ?>

                            </div>
                          </div>
                          <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                            <button type="submit" name="remark_submit" class="btn btn-primary">Save changes</button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </form>

                  <?php

                  if (isset($_POST['remark_submit'])) {
                    if (isset($_POST['remark'])) {
                      $work_ids = $_POST['work_ids'];
                      $remark = $_POST['remark']; // รับค่า remark จากฟอร์ม

                      // ใช้คำสั่ง SQL เพื่ออัปเดตข้อมูล
                      $stmt = $pdo->prepare("UPDATE inventory_onsite_log SET remark = :remark WHERE onsite_log_id = :work_id");
                      $stmt->bindParam(':remark', $remark, PDO::PARAM_STR); // ผูกค่า remark
                      $stmt->bindParam(':work_id', $work_ids, PDO::PARAM_INT); // ผูกค่า work_id
                      $stmt->execute();

                      echo "<script>
                                        window.location.href = 'work_view.php?work_id=$work_id'; // รีเฟรชหน้า
                                      </script>";
                    }
                  }


                  if (!empty($work_id)) {
                    // ดึงข้อมูลจากฐานข้อมูล
                    $stmt = $pdo->prepare("SELECT remark FROM inventory_onsite_log WHERE onsite_log_id = :work_id");
                    $stmt->bindParam(':work_id', $work_id);
                    $stmt->execute();
                    $result = $stmt->fetch(PDO::FETCH_ASSOC);

                    // ตรวจสอบว่าพบข้อมูล
                    if ($result && isset($result['remark']) && $result['remark'] !== null && $result['remark'] !== "") {
                      echo '<pre>' . $result['remark'] . '</pre>'; // แสดง remark หากมีข้อมูล
                    } else {
                      echo "-"; // แสดงข้อความเมื่อไม่มีข้อมูล
                    }
                  } else {
                    $work_id = "";
                    $remark = "ไม่มีข้อมูล";
                  }


                  ?>

                  <hr>

                  <h3>Customer Site</h3>
                  <?php

                  $get_data2 = file_get_contents('http://10.221.1.2/cs/technic/webservice/service_ticket_kcsdb.php?TOKEN=4d2e6435-6ad1-11ed-aabc-d4ae52873668&JN=' . $INso .'&NotWildcard=true');
                  $JN = json_decode($get_data2);
                  if ($JN->status == 'fail') {
                    echo "<p>Error: [$INso]" . $JN->message . "</p>";
                  }

                  //echo stdClass object
                  // Access the 'result' array within the stdClass object
                  $result = $JN->result;

                  // Access the first element of the 'result' array
                  $data = $result[0];


                  echo '<div>
                          <table class="table-sm table-striped table-hover table-bordered">
                              <tr>
                                <th>JN</th>
                                <td>' . $data->JN . '</td>
                              </tr>
                              <tr>
                                <th>Login</th>
                                <td>' . $data->Login . '</td>
                              </tr>
                              <tr>
                                <th>Domain Name</th>
                                <td>' . $data->Doamin_Name . '</td>
                              </tr>
                              <tr>
                                <th>Project ID</th>
                                <td>' . $data->Project_ID . '</td>
                              </tr>
                              <tr>
                                <th>Project Name</th>
                                <td>' . $data->Project_Name . '</td>
                              </tr>
                              <tr>
                                <th>Connect_No</th>
                                <td>' . $data->Connect_No . '</td>
                              </tr>
                              <tr>
                                <th>Connect_No2</th>
                                <td>' . $data->Connect_No2 . '</td>
                              </tr>
                              <tr>
                                <th>Site_Name</th>
                                <td>' . $data->Site_Name . '</td>
                              </tr>
                              <tr>
                                <th>Site_Address</th>
                                <td>' . $data->Site_Address . '</td>
                              </tr>
                              <tr>
                                <th>province_Name</th>
                                <td>' . $data->province_Name . '</td>
                              </tr>
                              <tr>
                                <th>LAN_IP</th>
                                <td>' . $data->LAN_IP . '</td>
                              </tr>
                              <tr>
                                <th>WAN_IP</th>
                                <td>' . $data->WAN_IP . '</td>
                              </tr>
                              <tr>
                                <th>Dum1</th>
                                <td>' . $data->Dum1 . '</td>
                              </tr>
                              <tr>
                                <th>Dum2</th>
                                <td>' . $data->Dum2 . '</td>
                              </tr>
                              <tr>
                                <th>WAN_Sub</th>
                                <td>' . $data->WAN_Sub . '</td>
                              </tr>
                              <tr>
                                <th>IROP</th>
                                <td>' . $data->IROP . '</td>
                              </tr>
                              <tr>
                                <th>Oprator_contact</th>
                                <td>' . $data->Oprator_contact . '</td>
                              </tr>
                              <tr>
                                <th>Status_Name</th>
                                <td>' . $data->Status_Name . '</td>
                              </tr>
                              <tr>
                                <th>Customer_contact</th>
                                <td>' . $data->Customer_contact . '</td>
                              </tr>
                              <tr>
                                <th>Phone1</th>
                                <td>' . $data->Phone1 . '</td>
                              </tr>
                              <tr>
                                <th>Oprator_Name</th>
                                <td>' . $data->Oprator_Name . '</td>
                              </tr>
                              <tr>
                                <th>Add_Date</th>
                                <td>' . $data->Add_Date . '</td>
                              </tr>
                            </table>
                            </div>
                            <hr>';


                  echo '<h3>Photo</h3>
                          <form id="uploadForm" action="../function/photoupload.php" method="post" enctype="multipart/form-data">
                            Select image:
                            <input type="file" name="images[]" multiple>
                            <input type="text" name="onsiteid" value="' . $onsiteid . '" hidden>
                            <input type="text" name="workid" value="' . $work_id . '" hidden>
                            <button type="submit" name="submit" class="btn btn-lg btn-info"><i class="fa-solid fa-image"></i> Upload</button><br>
                            <progress id="progressBar" value="0" max="100"></progress><div id="status"></div>
                          </form><br><br>
                          
                          <div class="row">';


                  $qrypic = mysqli_query($db, "SELECT * FROM upload WHERE pic_workid =" . $selectonsite_inv_id['onsite_id'] . " ");

                  while ($row = mysqli_fetch_array($qrypic)) {

                    echo '<div class="col-sm-2">' . $row['pic_name'] . '<br>
                              <a href="../Picstock/' . $onsiteid . '/' . $row['pic_name'] . '" data-toggle="lightbox" data-title="Photo of OnsiteID : ' . $onsiteid . '" data-gallery="gallery">
                              <img src="../Picstock/' . $onsiteid . '/' . $row['pic_name'] . '" class="img-fluid mb-2"/>
                              </a>
                            </div>';

                    // มีปุ่ม delete
                    /*echo '<div class="col-sm-2">
                              <a href="../function/photo_del.php?p_id='.$row['pic_id'].'&wo='.$row['pic_workid'].'&p_name='.$row['pic_name'].'&w_id='.$work_id.'"><i class="fa-solid fa-xmark"></i>Delete</a> '.$row['pic_name'].'<br>
                              <a href="../Picstock/'.$onsiteid.'/'.$row['pic_name'].'" data-toggle="lightbox" data-title="Photo of OnsiteID : '.$onsiteid.'" data-gallery="gallery">
                              <img src="../Picstock/'.$onsiteid.'/'.$row['pic_name'].'" class="img-fluid mb-2"/>
                              </a>
                            </div>';*/
                  }

                  ?>
                </div>
              </div>
            </div>
          </div>

        </div>
      </section>
      <!-- /.content -->
    </div>
    <!-- /.content-wrapper -->
    <!-- footer -->
    <?php include 'footer.php'; ?>
    <!-- /footer -->
    <!-- Control Sidebar -->
    <aside class="control-sidebar control-sidebar-dark">
      <!-- Control sidebar content goes here -->
    </aside>
    <!-- /.control-sidebar -->
  </div>
  <!-- ./wrapper -->

  <script>
    $.widget.bridge('uibutton', $.ui.button)


    // Function to update the progress bar
    function updateProgress(evt) {
      if (evt.lengthComputable) {
        var percentComplete = (evt.loaded / evt.total) * 100;
        document.getElementById('progressBar').value = percentComplete;
        document.getElementById('status').innerHTML = percentComplete + '%';
      }
    }

    // Attach an event listener to the form submission
    document.getElementById('uploadForm').addEventListener('submit', function(e) {
      e.preventDefault(); // Prevent the default form submission

      var form = e.target;
      var formData = new FormData(form);

      var xhr = new XMLHttpRequest();
      xhr.open('POST', form.action, true);

      // Event listener to track progress
      xhr.upload.addEventListener('progress', updateProgress, false);

      xhr.onreadystatechange = function() {
        if (xhr.readyState === 4 && xhr.status === 200) {
          // Handle the server response here if needed
          console.log(xhr.responseText);
          document.getElementById('status').innerHTML = 'Upload complete! Please Refresh.';
        }
      };

      xhr.send(formData);
    });
  </script>
  <!-- Page specific script -->
  <script>
    $(function() {
      $
      $(document).on('click', '[data-toggle="lightbox"]', function(event) {
        event.preventDefault();
        $(this).ekkoLightbox({
          alwaysShowClose: true
        });
      });
      /*
            $('.filter-container').filterizr({
              gutterPixels: 3
            });
      */
      $('.btn[data-filter]').on('click', function() {
        $('.btn[data-filter]').removeClass('active');
        $(this).addClass('active');
      });
    })
  </script>
</body>

</html>
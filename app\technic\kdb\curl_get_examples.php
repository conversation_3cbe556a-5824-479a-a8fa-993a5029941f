<?php
/**
 * cURL GET Method Examples
 * Different ways to make GET requests using cURL
 */

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Common settings
$token = 'jKqODgBPm1C5zw9CeiHx';
$base_url = "https://support.1-to-all.com/summary-tickets";

echo "<h2>cURL GET Method Examples</h2>";

// =============================================================================
// Method 1: Simple GET with query parameters in URL
// =============================================================================
echo "<h3>Method 1: Simple GET with Query Parameters</h3>";

$getData = array(
    'start_date' => '2025-06-01',
    'end_date' => '2025-06-30'
);

// Build query string
$queryString = http_build_query($getData);
$api_url = $base_url . '?' . $queryString;

echo "URL: " . $api_url . "<br><br>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $api_url);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    'Authorization: ' . $token,
    'Content-Type: application/json; charset=UTF-8'
));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPGET, true); // Explicitly set GET method
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // For HTTPS

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

echo "HTTP Code: " . $httpCode . "<br>";
echo "Response: " . htmlspecialchars($response) . "<br><br>";

if (curl_error($ch)) {
    echo "cURL Error: " . curl_error($ch) . "<br>";
}
curl_close($ch);

// =============================================================================
// Method 2: GET with manual URL building
// =============================================================================
echo "<h3>Method 2: Manual URL Building</h3>";

$start_date = '2025-06-01';
$end_date = '2025-06-30';
$manual_url = $base_url . "?start_date=" . urlencode($start_date) . "&end_date=" . urlencode($end_date);

echo "URL: " . $manual_url . "<br><br>";

$ch2 = curl_init();
curl_setopt($ch2, CURLOPT_URL, $manual_url);
curl_setopt($ch2, CURLOPT_HTTPHEADER, array('Authorization: ' . $token));
curl_setopt($ch2, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch2, CURLOPT_CUSTOMREQUEST, 'GET'); // Alternative way to set GET

$response2 = curl_exec($ch2);
$httpCode2 = curl_getinfo($ch2, CURLINFO_HTTP_CODE);

echo "HTTP Code: " . $httpCode2 . "<br>";
echo "Response: " . htmlspecialchars($response2) . "<br><br>";

curl_close($ch2);

// =============================================================================
// Method 3: GET with additional cURL options
// =============================================================================
echo "<h3>Method 3: GET with Additional Options</h3>";

function makeGetRequest($url, $params = array(), $headers = array()) {
    // Build URL with parameters
    if (!empty($params)) {
        $url .= '?' . http_build_query($params);
    }
    
    $ch = curl_init();
    
    // Basic cURL options
    curl_setopt_array($ch, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPGET => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_USERAGENT => 'PHP cURL GET Request',
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_MAXREDIRS => 3
    ));
    
    // Set headers if provided
    if (!empty($headers)) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    return array(
        'response' => $response,
        'http_code' => $httpCode,
        'error' => $error
    );
}

// Usage example
$params = array(
    'start_date' => '2025-06-01',
    'end_date' => '2025-06-30'
);

$headers = array(
    'Authorization: ' . $token,
    'Accept: application/json'
);

$result = makeGetRequest($base_url, $params, $headers);

echo "HTTP Code: " . $result['http_code'] . "<br>";
echo "Response: " . htmlspecialchars($result['response']) . "<br>";
if ($result['error']) {
    echo "Error: " . $result['error'] . "<br>";
}

// =============================================================================
// Method 4: GET with JSON response handling
// =============================================================================
echo "<h3>Method 4: GET with JSON Response Handling</h3>";

function getApiData($url, $params, $token) {
    $queryString = http_build_query($params);
    $fullUrl = $url . '?' . $queryString;
    
    $ch = curl_init();
    curl_setopt_array($ch, array(
        CURLOPT_URL => $fullUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPGET => true,
        CURLOPT_HTTPHEADER => array(
            'Authorization: ' . $token,
            'Accept: application/json',
            'User-Agent: API Client 1.0'
        ),
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYPEER => false
    ));
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    
    if (curl_error($ch)) {
        curl_close($ch);
        return array('error' => curl_error($ch));
    }
    
    curl_close($ch);
    
    // Handle response
    if ($httpCode >= 200 && $httpCode < 300) {
        $data = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            return array('success' => true, 'data' => $data);
        } else {
            return array('error' => 'Invalid JSON response');
        }
    } else {
        return array('error' => 'HTTP Error: ' . $httpCode, 'response' => $response);
    }
}

// Usage
$apiParams = array(
    'start_date' => '2025-06-01',
    'end_date' => '2025-06-30'
);

$apiResult = getApiData($base_url, $apiParams, $token);

if (isset($apiResult['success'])) {
    echo "Success! Data received:<br>";
    echo "<pre>" . print_r($apiResult['data'], true) . "</pre>";
} else {
    echo "Error: " . $apiResult['error'] . "<br>";
    if (isset($apiResult['response'])) {
        echo "Response: " . htmlspecialchars($apiResult['response']) . "<br>";
    }
}

echo "<hr>";
echo "<h3>Key Points for cURL GET Requests:</h3>";
echo "<ul>";
echo "<li><strong>CURLOPT_HTTPGET:</strong> Explicitly sets GET method</li>";
echo "<li><strong>CURLOPT_CUSTOMREQUEST:</strong> Alternative way to set method</li>";
echo "<li><strong>Query Parameters:</strong> Use http_build_query() or manual URL building</li>";
echo "<li><strong>No CURLOPT_POSTFIELDS:</strong> Remove this option for GET requests</li>";
echo "<li><strong>URL Encoding:</strong> Use urlencode() for special characters</li>";
echo "<li><strong>Headers:</strong> Set appropriate headers for API authentication</li>";
echo "</ul>";
?>

<?php
session_start();

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include PHPMailer
require_once 'vendor/autoload.php';
use <PERSON><PERSON>Mailer\PHPMailer\PHPMailer;
use <PERSON><PERSON>Mailer\PHPMailer\Exception;

$token = 'jKqODgBPm1C5zw9CeiHx';
$headers = array('Authorization: ' . $token . '', 'Content-Type: application/json; charset=UTF-8');
$api_url="https://support.1-to-all.com/summary-tickets";

// Configuration
$email_recipients = '<EMAIL>,<EMAIL>'; // Add your email recipients here

// Method 1: GET with query parameters in URL
$getData = array(
    'start_date' => '2025-06-01',
    'end_date' => '2025-06-30'
);

// Build query string for GET request
$queryString = http_build_query($getData);
$api_url_with_params = $api_url . '?' . $queryString;

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $api_url_with_params);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPGET, true); // Explicitly set GET method
// Remove CURLOPT_POSTFIELDS for GET request
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

if (in_array($httpCode, array('200', '201'))) {
    $resultData = json_decode($response, true);

    if ($resultData && $resultData['Status'] == 'Success') {
        echo "API Response received successfully<br>";

        // Format and send email
        $emailSent = sendTicketSummaryEmail($resultData, $email_recipients);

        if ($emailSent) {
            echo "Email sent successfully!<br>";
        } else {
            echo "Failed to send email<br>";
        }
    } else {
        echo "API returned error or invalid data<br>";
        echo "Response: " . htmlspecialchars($response) . "<br>";
    }
} else {
    echo "HTTP Error: " . $httpCode . "<br>";
    echo "cURL Error: " . curl_error($ch) . "<br>";
}

curl_close($ch);

/**
 * Format ticket summary data and send email
 * @param array $data The API response data
 * @param string $recipients Email recipients (comma-separated)
 * @return bool Success status
 */
function sendTicketSummaryEmail($data, $recipients) {
    try {
        $mail = new PHPMailer(true);

        // Server settings
        $mail->isSMTP();
        $mail->Host = '1-system.1-to-all.in.th';
        $mail->SMTPAuth = true;
        $mail->Username = '<EMAIL>';
        $mail->Password = '$InTh@5uPP0rt!!';
        $mail->Port = 587;
        $mail->SMTPSecure = '';

        // Disable certificate verification
        $mail->SMTPOptions = array(
            'ssl' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            )
        );

        // UTF-8 Settings
        $mail->CharSet = 'UTF-8';
        $mail->Encoding = 'base64';

        // Recipients
        $mail->setFrom('<EMAIL>', 'Ticket Summary System');

        // Add recipients
        $recipientList = explode(',', $recipients);
        foreach ($recipientList as $recipient) {
            $mail->addAddress(trim($recipient));
        }

        // Email content
        $subject = 'Ticket Summary Report - ' . formatDateRange($data['start_date'], $data['end_date']);
        $body = formatEmailBody($data);

        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $body;

        return $mail->send();

    } catch (Exception $e) {
        error_log("Email sending failed: " . $e->getMessage());
        echo "Email Error: " . $e->getMessage() . "<br>";
        return false;
    }
}

/**
 * Format the email body with ticket summary data
 * @param array $data The API response data
 * @return string HTML formatted email body
 */
function formatEmailBody($data) {
    $startDate = formatDate($data['start_date']);
    $endDate = formatDate($data['end_date']);
    $totalTickets = number_format($data['total_tickets']);
    $inProgressTickets = number_format($data['in_progress_tickets']);
    $closedTickets = number_format($data['closed_tickets']);

    // Calculate percentages
    $inProgressPercent = $data['total_tickets'] > 0 ? round(($data['in_progress_tickets'] / $data['total_tickets']) * 100, 1) : 0;
    $closedPercent = $data['total_tickets'] > 0 ? round(($data['closed_tickets'] / $data['total_tickets']) * 100, 1) : 0;

    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #2c3e50; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }
            .content { background-color: #f8f9fa; padding: 20px; border-radius: 0 0 5px 5px; }
            .summary-box { background-color: white; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 10px 0; }
            .metric { display: inline-block; text-align: center; margin: 10px; padding: 15px; background-color: #e9ecef; border-radius: 5px; min-width: 120px; }
            .metric-value { font-size: 24px; font-weight: bold; color: #2c3e50; }
            .metric-label { font-size: 12px; color: #6c757d; text-transform: uppercase; }
            .progress-bar { background-color: #e9ecef; border-radius: 10px; overflow: hidden; margin: 5px 0; }
            .progress-fill { height: 20px; background-color: #28a745; text-align: center; line-height: 20px; color: white; font-size: 12px; }
            .in-progress { background-color: #ffc107; }
            .footer { text-align: center; margin-top: 20px; font-size: 12px; color: #6c757d; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎫 Ticket Summary Report</h1>
                <p>Period: ' . $startDate . ' - ' . $endDate . '</p>
            </div>

            <div class="content">
                <div class="summary-box">
                    <h2>📊 Summary Overview</h2>

                    <div style="text-align: center;">
                        <div class="metric">
                            <div class="metric-value">' . $totalTickets . '</div>
                            <div class="metric-label">Total Tickets</div>
                        </div>

                        <div class="metric">
                            <div class="metric-value" style="color: #ffc107;">' . $inProgressTickets . '</div>
                            <div class="metric-label">In Progress</div>
                        </div>

                        <div class="metric">
                            <div class="metric-value" style="color: #28a745;">' . $closedTickets . '</div>
                            <div class="metric-label">Closed</div>
                        </div>
                    </div>
                </div>

                <div class="summary-box">
                    <h3>📈 Progress Breakdown</h3>

                    <p><strong>In Progress Tickets:</strong></p>
                    <div class="progress-bar">
                        <div class="progress-fill in-progress" style="width: ' . $inProgressPercent . '%;">
                            ' . $inProgressPercent . '% (' . $inProgressTickets . ' tickets)
                        </div>
                    </div>

                    <p><strong>Closed Tickets:</strong></p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ' . $closedPercent . '%;">
                            ' . $closedPercent . '% (' . $closedTickets . ' tickets)
                        </div>
                    </div>
                </div>

                <div class="summary-box">
                    <h3>📋 Raw Data</h3>
                    <pre style="background-color: #f8f9fa; padding: 10px; border-radius: 3px; font-size: 12px; overflow-x: auto;">
' . json_encode($data, JSON_PRETTY_PRINT) . '
                    </pre>
                </div>
            </div>

            <div class="footer">
                <p>Generated on ' . date('Y-m-d H:i:s') . ' | Ticket Summary System</p>
                <p>This is an automated email. Please do not reply.</p>
            </div>
        </div>
    </body>
    </html>';

    return $html;
}

/**
 * Format date for display
 * @param string $date Date string
 * @return string Formatted date
 */
function formatDate($date) {
    return date('F j, Y', strtotime($date));
}

/**
 * Format date range for subject
 * @param string $startDate Start date
 * @param string $endDate End date
 * @return string Formatted date range
 */
function formatDateRange($startDate, $endDate) {
    return date('M j', strtotime($startDate)) . ' - ' . date('M j, Y', strtotime($endDate));
}

<?php
session_start();

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

$token = 'jKqODgBPm1C5zw9CeiHx';
$headers = array('Authorization: ' . $token . '', 'Content-Type: application/json; charset=UTF-8');
//$api_url = "http://10.250.1.4/api/CreateTicketFromMonitor";
$api_url="https://support.1-to-all.com/summary-tickets";


// Method 1: GET with query parameters in URL
$getData = array(
    'start_date' => '2025-06-01',
    'end_date' => '2025-06-30'
);

// Build query string for GET request
$queryString = http_build_query($getData);
$api_url_with_params = $api_url . '?' . $queryString;

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $api_url_with_params);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPGET, true); // Explicitly set GET method
// Remove CURLOPT_POSTFIELDS for GET request
$response = curl_exec($ch);
var_dump($response);

if (in_array(curl_getinfo($ch, CURLINFO_HTTP_CODE), array('200', '201'))) {

    $resultData = json_decode($response, true);
    echo "Result: " . $resultData['Status'] . "<br />";
    
    /*
    if ($resultData['Status'] == 'Success') {
        $_SESSION["ticketno"] = $resultData['Ticket_No'];
    }
        */

} else {
    echo "Error: " . curl_error($ch);
}

curl_close($ch);

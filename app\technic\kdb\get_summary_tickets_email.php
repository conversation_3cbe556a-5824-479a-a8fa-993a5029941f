<?php

$token = 'jKqODgBPm1C5zw9CeiHx';
$headers = array('Authorization: ' . $token . '', 'Content-Type: application/json; charset=UTF-8');
//$api_url = "http://10.250.1.4/api/CreateTicketFromMonitor";
$api_url="https://support.1-to-all.com/summary-tickets";


$postData = array(
    'start_date' => '2025-06-01',
    'end_date' => '2025-06-30'
);

$fields = json_encode($postData, true);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $api_url);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
$response = curl_exec($ch);

if (in_array(curl_getinfo($ch, CURLINFO_HTTP_CODE), array('200', '201'))) {

    $resultData = json_decode($response, true);
    echo "Result: " . $resultData['Status'] . "<br />";
    var_dump($resultData);
    /*
    if ($resultData['Status'] == 'Success') {
        $_SESSION["ticketno"] = $resultData['Ticket_No'];
    }
        */

} else {
    echo "Error: " . curl_error($ch);
}

curl_close($ch);

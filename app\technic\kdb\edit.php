<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);

require_once '../../defined.conf.php';
require_once '../../model/authenCheck.php';
require_once "../db_config.php";

$RequestURI = $_SERVER['REQUEST_URI'];
$user = $_SESSION['User'];
$title = "CS-DB";
?>
<!doctype html>
<html lang="en">
<title>CS-DB Customer Editing</title>
<meta http-equiv="content-type" content="text/html; charset=tis-620" />
<link rel="icon" href="../img/Favicon.png" sizes="32x32">
<link rel="stylesheet" href="css/modal.css" type="text/css">
<link rel="stylesheet" href="../operation/opt.css" type="text/css">
</head>

<body>
    <?php
    if (!$user) {
        echo "<SCRIPT language=JavaScript>self.location='../login/index.php?request=$RequestURI';</SCRIPT>";
    }

    $conn = getDBConnection();
    mysqli_select_db($conn, 'KCS_DB');

    $cus_id = $_GET["cus_id"];
    $owner = '';
    switch (@$_POST['button']) {
        case "Cancel":
            echo "<SCRIPT language=JavaScript>self.location='index.php?scus_id=$cus_id';</SCRIPT>";
            break;
        case "Save":
            //var_dump($_POST);

            $project_id = $_POST["project_id"] ?? 0;
            $irop = $_POST["irop"];
            $site_type = $_POST["site_type"];
            $place_id = $_POST["place_id"] ?? 0;
            $sitename = $_POST["sitename"];
            $address = $_POST["address"];
            $ref_addr = $_POST["ref_addr"];

            $phone1 = $_POST["phone1"];
            $phone2 = $_POST["phone2"];
            $fax1 = $_POST["fax1"];
            $province_id = $_POST["province_id"] ?? 0;
            $ref_province_id = $_POST["ref_province_id"] ?? 0;

            $map = $_POST["map"];
            $zip = $_POST["zip"];

            $no1 = $_POST["no1"];
            $op1 = $_POST["op1"];
            $dum1 = $_POST["dum1"];
            $no2 = $_POST["no2"];
            $op2 = $_POST["op2"];
            $dum2 = $_POST["dum2"];
            $speed_id = $_POST["speed_id"] ?? 0;
            $dslin_id = $_POST["dslin_id"] ?? 0;

            $dns_in_date = $_POST["dns_in_date"] ?? '0000-00-00 00:00';
            $dns_in_date = fix_datetime($dns_in_date);

            $snr_m = $_POST["snr_m"];
            $snr_s = $_POST["snr_s"];
            $att_m = $_POST["att_m"];
            $att_s = $_POST["att_s"];
            $so = $_POST["so"];
            $req = $_POST["req"];
            $dnsname = $_POST["dnsname"];
            $dnsdomain = $_POST["dnsdomain"];

            $conntype_id = $_POST["conntype_id"] ?? 0;
            $ref_id = $_POST["ref_id"] ?? 0;
            $status_id = $_POST["status_id"] ?? 0;
            $login = $_POST["login"];
            $domain_id = $_POST["domain_id"] ?? 0;
            $pass = $_POST["pass"];
            $wan_ip = $_POST["wan_ip"];
            $wan_sub = $_POST["wan_sub"];

            $link = $_POST["link"];
            $link_domain = $_POST["link_domain"];
            $link_pass = $_POST["link_pass"];
            $link_ip = $_POST["link_ip"];
            $link_sub = $_POST["link_sub"];
            $tunnel2 = $_POST["tunnel2"];
            $t2sub = $_POST["t2sub"];
            $re_router = $_POST["re_router"];

            $bak_login = $_POST["bak_login"];
            $bak_domain = $_POST["bak_domain"];
            $bak_pass = $_POST["bak_pass"];
            $bak_ip = $_POST["bak_ip"];
            $bak_sub = $_POST["bak_sub"];
            $tunnel1 = $_POST["tunnel1"];
            $t1sub = $_POST["t1sub"];
            $install_date = $_POST["install_date"] ?? '0000-00-00 00:00:00';
            $install_date = fix_datetime($install_date);

            $equipment_id = $_POST["equipment_id"] ?? 0;
            $serial = $_POST["serial"];
            $antenna_id = $_POST["antenna_id"] ?? 0;
            $lan_ip = $_POST["lan_ip"];
            $lan_sub = $_POST["lan_sub"];
            $lan_ip2 = $_POST["lan_ip2"];
            $lan_sub2 = $_POST["lan_sub2"];
            $lan_ip3 = $_POST["lan_ip3"];
            $lan_sub3 = $_POST["lan_sub3"];
            $ups_id = $_POST["ups_id"];
            $ups_serial = $_POST["ups_serial"];

            $m1_id = $_POST["m1_id"] ?? 1;

            $m1_serial = $_POST["m1_serial"] ?? '';
            $m2_id = $_POST["m2_id"] ?? 1;
            $m2_serial = $_POST["m2_serial"] ?? '';
            $reboot_id = $_POST["reboot_id"] ?? 1;
            $reboot_serial = $_POST["reboot_serial"] ?? '';

            $vdsl = $_POST["vdsl"] ?? '';
            //echo $vdsl . "<br>";
            if ($vdsl) {
                $vdsl = 1;
            } else {
                $vdsl = 0;
            }
            //echo $vdsl . "<br>";
            $co_serial = $_POST["co_serial"] ?? '';
            $cpe_serial = $_POST["cpe_serial"] ?? '';
            $co2_serial = $_POST["co2_serial"] ?? '';
            $cpe2_serial = $_POST["cpe2_serial"] ?? '';
            $switch = $_POST["switch"] ?? '';
            if ($switch) {
                $switch = 1;
            } else {
                $switch = 0;
            }
            $sw_serial = @$_POST["sw_serial"];
            $sw2_serial = @$_POST["sw2_serial"];
            $profile_by = @$_POST["profile_by"];
            $rack_id = $_POST["rack_id"] ?? 1;
            $rack_serial = @$_POST["rack_serial"];
            $config_by = @$_POST["config_by"];
            $conf_date = $_POST["conf_date"] ?? '0000-00-00 00:00:00';
            $conf_date = fix_datetime($conf_date);

            $remark = @$_POST["remark"];
            $wan_gw = @$_POST['wan_gw'];
            $asn = @$_POST['asn'];

            $CORP_Interface = @trim(@$_POST['CORP_Interface']);
            $VSI_Name = @trim(@$_POST['VSI_Name']);
            $vpn_instance = @trim(@$_POST['vpn_instance']);
            $Node_ME = @trim(@$_POST['Node_ME']);
            $ME_Interface = @trim(@$_POST['ME_Interface']);

            $cuscode = @trim(@$_POST["cuscode"]);

            $EqMACAddr = $_POST["EqMACAddr"]??'';
            $MACAddr1 = $_POST["MACAddr1"]??'';
            $MACAddr2 = $_POST["MACAddr2"]??'';
            $MACAddr3 = $_POST["MACAddr3"]??'';


            /** for main */
            $sql = "UPDATE Main SET EqMACAddr='$EqMACAddr', CusCode = '$cuscode', Project_ID = '$project_id', IROP = '$irop', Ref = '$ref_id', Status_ID = '$status_id', Login = '$login', Domain_ID = '$domain_id', Password = '$pass', WAN_IP = '$wan_ip', WAN_Sub = '$wan_sub', Link_Login = '$link', Link_Domain_ID = '$link_domain', Link_Password = '$link_pass', Link_IP = '$link_ip'
        , Link_Sub = '$link_sub', Tunnel2 = '$tunnel2', Tunnel2sub = '$t2sub', Bak_Login = '$bak_login', Bak_Domain_ID = '$bak_domain', Bak_Password = '$bak_pass', Bak_IP = '$bak_ip', Bak_Sub = '$bak_sub', Tunnel1 = '$tunnel1', Tunnel1sub = '$t1sub', Connect_No = '$no1', Dum1 = '$dum1', Operator_ID = '$op1', Connect_No2 = '$no2', Dum2 = '$dum2'
        , Operator_ID2 = '$op2', Speed_ID = '$speed_id', DSL_Installation = '$dslin_id', DSL_Installation_Date = '$dns_in_date', Site_Type = '$site_type', Place_ID = '$place_id', Site_Name = '$sitename', Site_Address = '$address', Phone1 = '$phone1', Phone2 = '$phone2', FAX1 = '$fax1', Province_ID = '$province_id', Map = '$map', ZIP = '$zip'
        , Equipment_ID = '$equipment_id', Serial = '$serial', Antenna = '$antenna_id', LAN_IP = '$lan_ip', LAN_Sub = '$lan_sub', LAN_IP2 = '$lan_ip2', LAN_Sub2 = '$lan_sub2', LAN_IP3 = '$lan_ip3', LAN_Sub3 = '$lan_sub3', Restart_Router = '$re_router', Install_Date = '$install_date', Connection_Type = '$conntype_id', DownSNR = '$snr_m', UpSNR = '$snr_s'
        , DownAtt = '$att_m', UpAtt = '$att_s', Service_Order = '$so', Request = '$req', DDNS_Name = '$dnsname', DDNS_Domain_ID = '$dnsdomain', UPS_ID = '$ups_id', UPS_Serial = '$ups_serial', Modem1_ID = '$m1_id', Modem1_Ser = '$m1_serial', Modem2_ID = '$m2_id', Modem2_Ser = '$m2_serial', Reboot_ID = '$reboot_id', Reboot_Serial = '$reboot_serial'
        , VDSL = '$vdsl', Co_Serial = '$co_serial', CPE_Serial = '$cpe_serial', Co2_Serial = '$co2_serial', CPE2_Serial = '$cpe2_serial', Switch = '$switch', Switch_Serial = '$sw_serial', Switch2_Serial = '$sw2_serial', Rack_ID = '$rack_id', Rack_Serial = '$rack_serial', Config_By = '$config_by', Profile_By = '$profile_by', Config_Date = '$conf_date'
        , Remark = '$remark', WAN_GW='$wan_gw', ASN='$asn',CORP_Interface='$CORP_Interface',VSI_Name='$VSI_Name',vpn_instance='$vpn_instance',Node_ME='$Node_ME',ME_Interface='$ME_Interface'  WHERE Cus_ID = '$cus_id'";

            mysqli_query($conn, $sql);

            /** for main detal */
            $scus_id = $cus_id;
            $Contact_Sale = $_REQUEST['Contact_Sale'];
            $Contact_PM = $_REQUEST['Contact_PM'];
            $SLA = $_REQUEST['SLA'];
            $MTTR = $_REQUEST['MTTR'];
            $SMS_Alert = $_REQUEST['SMS_Alert'];
            $Mail_Alert = $_REQUEST['Mail_Alert'];
            $Monitor_Days = $_REQUEST['Monitor_Days'];
            $Monitor_Time = $_REQUEST['Monitor_Time'];
            $Surveillance_Type = $_REQUEST['Surveillance_Type'];
            $Network_Layer = $_REQUEST['Network_Layer'];
            $Client_Vlan_No = $_REQUEST['Vlan_No'];
            $Client_WAN = $_REQUEST['IP_WAN'];
            $Client_GW = $_REQUEST['IP_GW'];
            $Client_Radius_User = $_REQUEST['Radius_User'];
            $Client_Radius_Pass = $_REQUEST['Radius_Pass'];
            $MPLS_Node = $_REQUEST['MPLS_Node'];
            $Jinet_Vlan_No = $_REQUEST['Jinet_Vlan_No'];
            $Equipment1 = $_REQUEST['Equipment1_name'];
            $Equipment1_SN = $_REQUEST['Equipment1_sn'];
            $Equipment2 = $_REQUEST['Equipment2_name'];
            $Equipment2_SN = $_REQUEST['Equipment2_sn'];
            $Equipment3 = $_REQUEST['Equipment3_name'];
            $Equipment3_SN = $_REQUEST['Equipment3_sn'];
            $Client_LAN1 = @$_REQUEST['IP_LAN1'];
            $Client_LAN1_Subnet = @$_REQUEST['IP_LAN1_SUB'];
            $Client_LAN1_VIP1 = $_REQUEST['IP_VIP1'];
            $Client_LAN2 = @$_REQUEST['IP_LAN2'];
            $Client_LAN2_Subnet = @$_REQUEST['IP_LAN2_SUB'];
            $Client_LAN2_VIP2 = $_REQUEST['IP_VIP2'];
            $Client_LAN3 = @$_REQUEST['IP_LAN3'];
            $Client_LAN3_Subnet = @$_REQUEST['IP_LAN3_SUB'];
            $Client_LAN3_VIP3 = $_REQUEST['IP_VIP3'];
            $Client_LAN4 = @$_REQUEST['IP_LAN4'];
            $Client_LAN4_Subnet = @$_REQUEST['IP_LAN4_SUB'];
            $Client_LAN4_VIP4 = $_REQUEST['IP_VIP4'];
            $URL_CATCI_MON = $_REQUEST['URL_CATCI_MON'];
            $URL_PRTG_MON = $_REQUEST['URL_PRTG_MON'];
            $URL_WU_MON = $_REQUEST['URL_WU_MON'];
            $Mon_User_Pass = $_REQUEST['MON_User_Pass'];
            $URL_Backup_Config = $_REQUEST['URL_Backup_Config'];

            $sql_main_detail = "REPLACE INTO Main_Detail(`scus_id`,
	`Contact_Sale` ,
	`Contact_PM`,
	`SLA`,
	`MTTR`,
	`SMS_Alert`,
	`Mail_Alert`,
	`Monitor_Days`,
	`Monitor_Time`,
	`Surveillance_Type`,
	`Network_Layer`,
	`Client_Vlan_No`,
	`Client_WAN`,
	`Client_GW`,
	`Client_Radius_User`,
	`Client_Radius_Pass`,
	`MPLS_Node`,
	`Jinet_Vlan_No`,
	`Equipment1`,
	`Equipment1_SN`,
	`Equipment2`,
	`Equipment2_SN`,
	`Equipment3`,
	`Equipment3_SN`,`MACAddr1`,`MACAddr2`,`MACAddr3`,
	`Client_LAN1`,
	`Client_LAN1_Subnet`,
	`Client_LAN1_VIP1`,
	`Client_LAN2`,
	`Client_LAN2_Subnet`,
	`Client_LAN2_VIP2`,
	`Client_LAN3`,
	`Client_LAN3_Subnet`,
	`Client_LAN3_VIP3`,
	`Client_LAN4`,
	`Client_LAN4_Subnet`,
	`Client_LAN4_VIP4`,
	`URL_CATCI_MON`,
	`URL_PRTG_MON`,
	`URL_WU_MON`,
	`Mon_User_Pass`,
	`URL_Backup_Config` ) VALUES('$scus_id',
	'$Contact_Sale' ,
	'$Contact_PM',
	'$SLA',
	'$MTTR',
	'$SMS_Alert',
	'$Mail_Alert',
	'$Monitor_Days',
	'$Monitor_Time',
	'$Surveillance_Type',
	'$Network_Layer',
	'$Client_Vlan_No',
	'$Client_WAN',
	'$Client_GW',
	'$Client_Radius_User',
	'$Client_Radius_Pass',
	'$MPLS_Node',
	'$Jinet_Vlan_No',
	'$Equipment1',
	'$Equipment1_SN',
	'$Equipment2',
	'$Equipment2_SN',
	'$Equipment3',
	'$Equipment3_SN','$MACAddr1','$MACAddr2','$MACAddr3',
	'$Client_LAN1',
	'$Client_LAN1_Subnet',
	'$Client_LAN1_VIP1',
	'$Client_LAN2',
	'$Client_LAN2_Subnet',
	'$Client_LAN2_VIP2',
	'$Client_LAN3',
	'$Client_LAN3_Subnet',
	'$Client_LAN3_VIP3',
	'$Client_LAN4',
	'$Client_LAN4_Subnet',
	'$Client_LAN4_VIP4',
	'$URL_CATCI_MON',
	'$URL_PRTG_MON',
	'$URL_WU_MON',
	'$Mon_User_Pass',
	'$URL_Backup_Config');";

            mysqli_query($conn, $sql_main_detail);

            $sql_update_montype = " UPDATE Main SET Project_Mon_Type_ID='$Surveillance_Type' ,Project_Mon_Date_ID='$Monitor_Days', Project_Mon_Time_ID='$Monitor_Time'  WHERE Cus_ID=$cus_id ;";
            mysqli_query($conn, $sql_update_montype);

            if ($address <> $ref_addr || $province_id <> $ref_province_id) {
                include_once 'pay_expenses_func.php';
                $pay_expense = get_pay_expenses($address, $province_id);
                $sql_pay_expense = "UPDATE Main SET Price = $pay_expense WHERE Cus_ID=$cus_id  ;";
                mysqli_query($conn, $sql_pay_expense);
            }
            /*
              //1toall,jinet
              $project_owner = $_REQUEST['owner'];
              $sql_project_update ="UPDATE Project SET `Owner`='$project_owner' WHERE Project_ID=  $project_id;";
              mysql_query($sql_project_update);
             */

            echo "<SCRIPT language=JavaScript>self.location='index.php?scus_id=$cus_id';</SCRIPT>";
            break;
    }

    $cus_query = mysqli_query($conn, "SELECT m.*,c.CusName FROM  Main m LEFT JOIN Customers  c ON m.CusCode = c.CusCode  WHERE  Cus_ID = '$cus_id'");
    // $cus = mysql_fetch_row($cus_query);

    $cus = mysqli_fetch_array($cus_query, MYSQLI_BOTH);
    /*
  var_dump($cus);
  echo "<br><br>main_detail >> <br>";
 */

    if ($cus['Cus_ID']) {
        $sql_main_detail = "SELECT * FROM Main_Detail WHERE scus_id='" . $cus['Cus_ID'] . "';";
        $main_detail_rst = mysqli_query($conn, $sql_main_detail);
        $main_detail = mysqli_fetch_array($main_detail_rst, MYSQLI_BOTH);

        //echo "<p> sql_main_detail= $sql_main_detail </p>";
        // var_dump($main_detail);

        if ($main_detail) {
            $cus = array_merge($cus, $main_detail);
        }

        $sql_mon = "SELECT  mt.Project_Mon_Type_Name,md.Project_Mon_Date_Name,mtm.Project_Mon_Time_Name
                            FROM Main m
                            LEFT JOIN Project_Mon_Type mt ON m.Project_Mon_Type_ID = mt.Project_Mon_Type_ID
                            LEFT JOIN Project_Mon_Day md ON m.Project_Mon_Date_ID = md.Project_Mon_Date_ID
                            LEFT JOIN Project_Mon_Time mtm ON m.Project_Mon_Time_ID = mtm.Project_Mon_Time_ID
                            WHERE
                            m.Cus_ID =" . $cus['Cus_ID'] . "; ";

        $monitor_rst = mysqli_query($conn, $sql_mon);
        $monitor = mysqli_fetch_array($monitor_rst, MYSQLI_BOTH);
        if ($monitor) {
            $cus = array_merge($cus, $monitor);
        }
    }

    // var_dump($cus); 
    // print_r($cus);
    //exit;
    ?>
    <div id="myModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h5 class="mod_caption">Search & Select</h5>
            <div class="modal-body">
                <div id="mod_toolbar" class="mod_toolbar">
                    <input type="text" name="mod_txtsearch" id="mod_txtsearch" style="width: 400px; height: 20px;" placeholder="please enter text to search">
                    <button id="mod_btn_search" type="button">Search</button>
                </div><br>
                <div id="mod_results" class="mod_results">

                </div>
            </div>
        </div>
    </div>
    <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#ffddbb">
        <form name="form1" method="post" action="?cus_id=<?php echo $cus_id ?>">
            <input type="hidden" id="ref_addr" name="ref_addr" value="<?php echo $cus['Site_Address'] ?>">
            <input type="hidden" id="ref_province_id" name="ref_province_id" value="<?php echo $cus['Province_ID'] ?>">

            <tr>
                <td class="bbbody" style="padding-left:15px; padding-right:15px"><br />ADDRESS / NUMBER CONNECTION<br /><br />
                    <table border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td height="24" align="right" class="bbody" style="padding:5px">Project</td>
                            <td>
                                <select name="project_id" id="project_id"
                                    <?php
                                    if ($_SESSION['kdb'] < 9) {
                                        echo "style=\"background:#DDDDDD\" disabled";
                                    }
                                    ?>>
                                    <?php
                                    $owner = '';
                                    $project_query = mysqli_query($conn, "select Project_ID, Project_Name,`Owner`AS Project_Owner from Project order by Project_Name");
                                    while ($project = mysqli_fetch_array($project_query, MYSQLI_BOTH)) {
                                        $selected_project = '';
                                        if ($project['Project_ID'] == $cus['Project_ID']) {
                                            $selected_project = 'SELECTED';
                                            $owner = $project['Project_Owner'];
                                        }
                                        echo '<option data-owner="' . $project['Project_Owner'] . '" VALUE="' . $project['Project_ID'] . '"  ' . $selected_project . '> ' . $project['Project_Name'] . '</option>';
                                    }
                                    ?>
                                </select>

                            </td>
                            <td class="bbody" align="right" style="padding:5px">IROP</td>
                            <td>
                                <input name="irop" type="text" id="irop" value="<?php echo $cus['IROP'] ?>"
                                    <?php
                                    if ($_SESSION['kdb'] < 9) {
                                        echo "style=\"background:#DDDDDD\" disabled";
                                    }
                                    ?>>
                            </td>
                            <td class="bbody" align="right" style="padding:5px;"><label for="cuscode"> Cus.Code(CTD)</label></td>
                            <td>
                                <input name="cuscode" type="text" id="cuscode" value="<?php echo $cus['CusCode'] ?>">
                                <button id="btnShowPopup" type="button">...</button>
                            </td>
                        </tr>
                    </table><br /><br />
                    <table border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="140" height="24" align="right" class="bbody" style="padding-right:5px">Customer Name</td>
                                        <td colspan="3" bgcolor="#ffcc99" style="padding-left:5px; border:solid #999 1px" class="bbody">
                                            <label id="cusname"><?php echo $cus['CusName'] ?></label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td width="140" height="24" align="right" class="bbody" style="padding-right:5px">CS ID</td>
                                        <td width="100" bgcolor="#ffcc99" style="padding-left:5px; border:solid #999 1px" class="bbody">
                                            <?php echo $cus['Cus_ID'] ?>&nbsp;
                                        </td>
                                        <td width="50" class="bbody" align="right" style="padding-right:5px">Owner</td>

                                        <td bgcolor="#ffcc99" style="padding-left:5px; border:solid #999 1px" class="bbody">
                                            <?php echo $owner ?>
                                        </td>

                                    </tr>
                                    <tr>
                                        <td width="140" height="24" align="right" class="bbody" style="padding-right:5px">Type</td>
                                        <td><select name="site_type" id="site_type" <?php
                                                                                    if ($_SESSION['kdb'] < 5) {
                                                                                        echo "style=\"background:#DDDDDD\" disabled";
                                                                                    }
                                                                                    ?> style="height: 24px;">
                                                <?php
                                                $sitetype_query = mysqli_query($conn, "select Site_ID, Site_Name from Site_Type order by Site_Name");
                                                while ($sitetype = mysqli_fetch_array($sitetype_query, MYSQLI_BOTH)) {
                                                    echo '<option VALUE="' . $sitetype['Site_ID'] . '" ' . ($sitetype['Site_ID'] == $cus['Site_Type'] ? "SELECTED" : "") . '> ' . $sitetype['Site_Name'] . '</option>';
                                                }
                                                ?>
                                            </select></td>
                                        <td width="50" class="bbody" align="right" style="padding-right:5px">Place</td>
                                        <td><select name="place_id" id="place_id" <?php
                                                                                    if ($_SESSION['kdb'] < 5) {
                                                                                        echo "style=\"background:#DDDDDD\" disabled";
                                                                                    }
                                                                                    ?> style="height: 24px;">
                                                <?php
                                                $place_query = mysqli_query($conn, "select Shop_ID, Shop_Name from Place order by Shop_Name");
                                                while ($place = mysqli_fetch_array($place_query, MYSQLI_BOTH)) {
                                                    echo '<option VALUE="' . $place['Shop_ID'] . '" ' . ($place['Shop_ID'] == $cus['Place_ID'] ? "SELECTED" : "") . '> ' . $place['Shop_Name'] . '</option>';
                                                }
                                                ?>
                                            </select></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="140" height="24" align="right" class="bbody" style="padding-right:5px">Site Name</td>
                                        <td><input style="width:530px;" name="sitename" type="text" id="sitename" value="<?php echo $cus['Site_Name'] ?>"
                                                <?php
                                                if ($_SESSION['kdb'] < 5) {
                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                }
                                                ?>></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="140" height="24" align="right" class="bbody" style="padding-right:5px">Site Address</td>
                                        <td rowspan="3"><textarea name="address" id="address" cols="73" rows="5"
                                                <?php
                                                if ($_SESSION['kdb'] < 5) {
                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                }
                                                ?>><?php echo $cus['Site_Address'] ?></textarea></td>
                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="140" height="24" align="right" class="bbody" style="padding-right:5px">
                                            1 ST_Customer Contact</td>
                                        <td rowspan="2"><textarea name="phone1" id="phone1" cols="73" rows="3"
                                                <?php
                                                if ($_SESSION['kdb'] < 3) {
                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                }
                                                ?>><?php echo $cus['Phone1'] ?></textarea></td>
                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="140" height="24" align="right" class="bbody" style="padding-right:5px">
                                            2 ND_Customer Contact</td>
                                        <td rowspan="2"><textarea name="phone2" id="phone2" cols="73" rows="3"
                                                <?php
                                                if ($_SESSION['kdb'] < 3) {
                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                }
                                                ?>><?php echo $cus['Phone2'] ?></textarea></td>
                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="140" height="24" align="right" class="bbody" style="padding-right:5px">Contact Sale</td>
                                        <td width="445">
                                            <input name="Contact_Sale" id="Contact_Sale" type="text" value="<?php echo @$cus['Contact_Sale'] ?>">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td width="140" height="24" align="right" class="bbody" style="padding-right:5px">Contact PM</td>
                                        <td width="445"><input name="Contact_PM" id="Contact_PM" type="text" value="<?php echo @$cus['Contact_PM'] ?>"></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="140" align="right" rowspan="2" class="bbody" style="padding-right:5px" valign="top">Terms of Service</td>
                                        <td align="right" class="bbody" style="padding-right:5px" width="60">SLA</td>
                                        <td width="150">
                                            <input name="SLA" id="SLA" type="text" value="<?php echo @$cus['SLA'] ?>">
                                        </td>
                                        <td align="right" class="bbody" style="padding-right:5px">SMS Alert</td>
                                        <td width="150">
                                            <input name="SMS_Alert" id="SMS_Alert" type="text" value="<?php echo @$cus['SMS_Alert'] ?>">
                                        </td>
                                        <td> </td>
                                    </tr>
                                    <tr>

                                        <td align="right" class="bbody" style="padding-right:5px">MTTR</td>
                                        <td>
                                            <input name="MTTR" id="MTTR" type="text" value="<?php echo @$cus['MTTR'] ?>">
                                        </td>
                                        <td align="right" class="bbody" style="padding-right:5px">Mail Alert</td>
                                        <td>
                                            <input name="Mail_Alert" id="Mail_Alert" type="text" value="<?php echo @$cus['Mail_Alert'] ?>">
                                        </td>
                                        <td> </td>
                                    </tr>

                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="140" align="right" class="bbody" style="padding-right:5px" valign="top">Surveillance Type</td>
                                        <td width="94">

                                            <select style="width:150px;margin-right:10px;margin-left:5px" name="Surveillance_Type" id="Surveillance_Type"
                                                <?php
                                                if ($_SESSION['kdb'] < 5) {
                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                }
                                                ?>>
                                                <?php
                                                $Mon_Type_query = mysqli_query($conn, "SELECT Project_Mon_Type_ID,Project_Mon_Type_Name FROM Project_Mon_Type");
                                                while ($Mon_Type = mysqli_fetch_array($Mon_Type_query, MYSQLI_BOTH)) {
                                                    echo '<option VALUE="' . $Mon_Type['Project_Mon_Type_ID'] . '" ' . ($Mon_Type['Project_Mon_Type_ID'] == $cus['Project_Mon_Type_ID'] ? "SELECTED" : "") . '> ' . $Mon_Type['Project_Mon_Type_Name'] . '</option>';
                                                }
                                                ?>
                                            </select>

                                        </td>

                                        <td align="right" class="bbody" style="padding-right:5px">Monitor Days</td>
                                        <td width="70">

                                            <select style="width:150px;margin-right:10px;margin-left:5px;" name="Monitor_Days" id="Monitor_Days"
                                                <?php
                                                if ($_SESSION['kdb'] < 5) {
                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                }
                                                ?>>
                                                <?php
                                                $Mon_Type_query = mysqli_query($conn, "SELECT Project_Mon_Date_ID,Project_Mon_Date_Name FROM Project_Mon_Day");
                                                while ($Mon_Type = mysqli_fetch_array($Mon_Type_query, MYSQLI_BOTH)) {
                                                    echo '<option VALUE="' . $Mon_Type['Project_Mon_Date_ID'] . '" ' . ($Mon_Type['Project_Mon_Date_ID'] == $cus['Project_Mon_Date_ID'] ? "SELECTED" : "") . '> ' . $Mon_Type['Project_Mon_Date_Name'] . '</option>';
                                                }
                                                ?>
                                            </select>

                                        </td>

                                        <td align="right" class="bbody" style="padding-right:5px">Monitor Time</td>
                                        <td width="94">

                                            <select style="width:150px;margin-right:10px;margin-left:5px" name="Monitor_Time" id="Monitor_Time"
                                                <?php
                                                if ($_SESSION['kdb'] < 5) {
                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                }
                                                ?>>
                                                <?php
                                                $Mon_Type_query = mysqli_query($conn, "SELECT Project_Mon_Time_ID,Project_Mon_Time_Name FROM Project_Mon_Time");
                                                while ($Mon_Type = mysqli_fetch_array($Mon_Type_query, MYSQLI_BOTH)) {
                                                    echo '<option VALUE="' . $Mon_Type['Project_Mon_Time_ID'] . '" ' . ($Mon_Type['Project_Mon_Time_ID'] == $cus['Project_Mon_Time_ID'] ? "SELECTED" : "") . '> ' . $Mon_Type['Project_Mon_Time_Name'] . '</option>';
                                                }
                                                ?>
                                            </select>

                                        </td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="140" height="24" align="right" class="bbody" style="padding-right:5px">FAX1</td>
                                        <td><input name="fax1" type="text" id="fax1" value="<?php echo $cus['FAX1'] ?>"
                                                <?php
                                                if ($_SESSION['kdb'] < 5) {
                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                }
                                                ?>></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="140" height="24" align="right" class="bbody" style="padding-right:5px">Province</td>
                                        <td>
                                            <select name="province_id" id="province_id" <?php
                                                                                        if ($_SESSION['kdb'] < 5) {
                                                                                            echo "style=\"background:#DDDDDD\" disabled";
                                                                                        }
                                                                                        ?>>
                                                <?php
                                                $province_query = mysqli_query($conn, "select province_ID, province_Name from Province order by province_Name");
                                                while ($province = mysqli_fetch_array($province_query, MYSQLI_BOTH)) {
                                                    echo '<option VALUE="' . $province['province_ID'] . '" ' . ($province['province_ID'] == $cus['Province_ID'] ? "SELECTED" : "") . '> ' . $province['province_Name'] . '</option>';
                                                }
                                                ?>
                                            </select>
                                        </td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="100" height="24" align="right" class="bbody" style="padding-right:5px">Map</td>
                                        <td><input name="map" type="text" id="map" value="<?= $cus['Map'] ?>"
                                                <?php
                                                if ($_SESSION['kdb'] < 5) {
                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                }
                                                ?> size="40"></td>
                                        <td width="50" align="right" class="bbody" style="padding-right:5px">ZIP</td>
                                        <td><input name="zip" type="text" id="zip" value="<?php echo $cus['ZIP'] ?>"
                                                <?php
                                                if ($_SESSION['kdb'] < 5) {
                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                }
                                                ?>></td>
                                    </tr>
                                </table>
                            </td>
                            <td valign="top" style="padding-left:50px">
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="50" height="24" align="right" class="bbody" style="padding-right:5px">No.1</td>
                                        <td><input name="no1" type="text" id="no1" value="<?php echo $cus['Connect_No'] ?>" size="15" maxlength="15"
                                                <?php
                                                if ($_SESSION['kdb'] < 4) {
                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                }
                                                ?>></td>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px; padding-left:5px">Dummy1</td>
                                        <td><input name="dum1" type="text" id="dum1" value="<?php echo $cus['Dum1'] ?>" size="15" maxlength="15"
                                                <?php
                                                if ($_SESSION['kdb'] < 4) {
                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                }
                                                ?>></td>
                                        <td><select name="op1" id="op1" <?php
                                                                        if ($_SESSION['kdb'] < 4 || $_SESSION['kdb'] == 6) {
                                                                            echo "style=\"background:#DDDDDD\" disabled";
                                                                        }
                                                                        ?>>
                                                <?php
                                                $oprator_contact = "";
                                                $operator_query = mysqli_query($conn, "select Oprator_ID, Oprator_Name,Oprator_contact  from Oprator order by Oprator_Name");
                                                while ($operator = mysqli_fetch_array($operator_query, MYSQLI_BOTH)) {
                                                    if ($operator['Oprator_ID'] == $cus['Operator_ID']) {
                                                        $oprator_contact = $operator['Oprator_contact'];
                                                    }
                                                    echo '<option  data-contact="' . $operator['Oprator_contact'] . '" value="' . $operator['Oprator_ID'] . '" ' . ($operator['Oprator_ID'] == $cus['Operator_ID'] ? "SELECTED" : "") . '> ' . $operator['Oprator_Name'] . '</option>';
                                                }
                                                ?>
                                            </select></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="50" height="24" align="right" class="bbody" style="padding-right:5px">No.2</td>
                                        <td><input name="no2" type="text" id="no2" value="<?php echo $cus['Connect_No2'] ?>" size="15" maxlength="15"
                                                <?php
                                                if ($_SESSION['kdb'] < 4) {
                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                }
                                                ?>></td>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px; padding-left:5px">Dummy2</td>
                                        <td><input name="dum2" type="text" id="dum2" value="<?php echo $cus['Dum2'] ?>" size="15" maxlength="15"
                                                <?php
                                                if ($_SESSION['kdb'] < 4) {
                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                }
                                                ?>></td>
                                        <td><select name="op2" id="op2" <?php
                                                                        if ($_SESSION['kdb'] < 4 || $_SESSION['kdb'] == 6) {
                                                                            echo "style=\"background:#DDDDDD\" disabled";
                                                                        }
                                                                        ?>>
                                                <?php
                                                $operator_query = mysqli_query($conn, "select Oprator_ID, Oprator_Name,Oprator_contact  from Oprator order by Oprator_Name");
                                                while ($operator = mysqli_fetch_array($operator_query, MYSQLI_BOTH)) {
                                                    echo '<option data-contact="' . $operator['Oprator_contact'] . '" value="' . $operator['Oprator_ID'] . '" ' . ($operator['Oprator_ID'] == $cus['Operator_ID2'] ? "SELECTED" : "") . '> ' . $operator['Oprator_Name'] . '</option>';
                                                }
                                                ?>

                                            </select></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="93" height="24" align="right" class="bbody" style="padding-right:5px">Speed</td>
                                        <td width="200">
                                            <select name="speed_id" id="speed_id" <?php
                                                                                    if ($_SESSION['kdb'] < 5 || $_SESSION['kdb'] == 6 || $_SESSION['kdb'] == 7) {
                                                                                        echo "style=\"background:#DDDDDD\" disabled";
                                                                                    }
                                                                                    ?>>
                                                <?php
                                                $speed_query = mysqli_query($conn, "select Speed_ID, Speed_Name from Speed order by Speed_Sort");
                                                while ($speed = mysqli_fetch_array($speed_query, MYSQLI_BOTH)) {
                                                    echo '<option VALUE="' . $speed['Speed_ID'] . '" ' . ($speed['Speed_ID'] == $cus['Speed_ID'] ? "SELECTED" : "") . '> ' . $speed['Speed_Name'] . '</option>';
                                                }
                                                ?>
                                            </select>
                                        </td>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Network Layer</td>
                                        <td width="150">
                                            <input name="Network_Layer" id="Network_Layer" type="text" value="<?php echo @$cus['Network_Layer'] ?>">
                                        </td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="100" height="24" align="right" class="bbody" style="padding-right:5px">Vlan No.</td>
                                        <td width="90">
                                            <input name="Vlan_No" id="Vlan_No" type="text" value="<?php echo @$cus['Client_Vlan_No'] ?>">
                                        </td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">IP WAN</td>
                                        <td width="90">
                                            <input name="IP_WAN" id="IP_WAN" type="text" value="<?php echo @$cus['Client_WAN'] ?>">
                                        </td>
                                        <td></td>
                                        <td></td>
                                    </tr>

                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">IP GW</td>
                                        <td width="90">
                                            <input name="IP_GW" id="IP_GW" type="text" value="<?php echo @$cus['Client_GW'] ?>">
                                        </td>
                                        <td></td>
                                        <td></td>
                                    </tr>

                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Radius User/Pass</td>
                                        <td width="180">
                                            <input name="Radius_User" id="Radius_User" type="text" value="<?php echo @$cus['Client_Radius_User'] ?>">
                                        </td>

                                        <td height="24" align="right" class="bbody" style="padding-right:5px" width="16">/</td>
                                        <td width="180">
                                            <input name="Radius_Pass" id="Radius_Pass" type="text" value="<?php echo @$cus['Client_Radius_Pass'] ?>">
                                        </td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="100" height="24" align="right" class="bbody" style="padding-right:5px">DSL Installation</td>
                                        <td><select name="dslin_id" id="dslin_id" <?php
                                                                                    if ($_SESSION['kdb'] <> 9 && $_SESSION['kdb'] <> 5) {
                                                                                        echo "style=\"background:#DDDDDD\" disabled";
                                                                                    }
                                                                                    ?>>
                                                <?php
                                                $dslin_query = mysqli_query($conn, "select Status_ID, Status_Name from DSL_Installation order by Status_Name");
                                                while ($dslin = mysqli_fetch_array($dslin_query, MYSQLI_BOTH)) {
                                                    echo '<option VALUE="' . $dslin['Status_ID'] . '" ' . ($dslin['Status_ID'] == $cus['DSL_Installation'] ? "SELECTED" : "") . '> ' . $dslin['Status_Name'] . '</option>';
                                                }
                                                ?>
                                            </select></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="100" height="24" align="right" class="bbody" style="padding-right:5px">DSL Installation Date</td>
                                        <td><input name="dns_in_date" type="text" id="dns_in_date" value="<?php echo $cus['DSL_Installation_Date'] ?>" <?php
                                                                                                                                                        if ($_SESSION['kdb'] <> 9 && $_SESSION['kdb'] <> 5) {
                                                                                                                                                            echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                                        }
                                                                                                                                                        ?>></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="100" height="24" align="right" class="bbody" style="padding-right:5px">SNR</td>
                                        <td><input name="snr_m" type="text" id="snr_m" value="<?php echo $cus['DownSNR'] ?>" <?php
                                                                                                                                if ($_SESSION['kdb'] < 6) {
                                                                                                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                }
                                                                                                                                ?>></td>
                                        <td class="bbody" style="padding-right:5px; padding-left:5px">/</td>
                                        <td><input name="snr_s" type="text" id="snr_s" value="<?php echo $cus['UpSNR'] ?>" <?php
                                                                                                                            if ($_SESSION['kdb'] < 6) {
                                                                                                                                echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                            }
                                                                                                                            ?>></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="100" height="24" align="right" class="bbody" style="padding-right:5px">Att</td>
                                        <td><input name="att_m" type="text" id="att_m" value="<?php echo $cus['DownAtt'] ?>" <?php
                                                                                                                                if ($_SESSION['kdb'] < 6) {
                                                                                                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                }
                                                                                                                                ?>></td>
                                        <td class="bbody" style="padding-right:5px; padding-left:5px">/</td>
                                        <td><input name="att_s" type="text" id="att_s" value="<?php echo $cus['UpAtt'] ?>" <?php
                                                                                                                            if ($_SESSION['kdb'] < 6) {
                                                                                                                                echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                            }
                                                                                                                            ?>></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="100" height="24" align="right" class="bbody" style="padding-right:5px">Service Order</td>
                                        <td><input name="so" type="text" id="so" value="<?php echo $cus['Service_Order'] ?>" <?php
                                                                                                                                if ($_SESSION['kdb'] < 5 || $_SESSION['kdb'] == 6) {
                                                                                                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                }
                                                                                                                                ?>></td>
                                        <td class="bbody" style="padding-right:5px; padding-left:5px">Req</td>
                                        <td><input name="req" type="text" id="req" value="<?php echo $cus['Request'] ?>" <?php
                                                                                                                            if ($_SESSION['kdb'] < 5 || $_SESSION['kdb'] == 6) {
                                                                                                                                echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                            }
                                                                                                                            ?>></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="100" height="24" align="right" class="bbody" style="padding-right:5px">DDNS Name</td>
                                        <td><input name="dnsname" type="text" id="dnsname" value="<?php echo $cus['DDNS_Name'] ?>" <?php
                                                                                                                                    if ($_SESSION['kdb'] <> 9 && $_SESSION['kdb'] <> 5) {
                                                                                                                                        echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                    }
                                                                                                                                    ?>></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="100" height="24" align="right" class="bbody" style="padding-right:5px">DDNS Domain</td>
                                        <td>
                                            <select name="dnsdomain" id="dnsdomain" <?php
                                                                                    if ($_SESSION['kdb'] <> 9 && $_SESSION['kdb'] <> 5) {
                                                                                        echo "style=\"background:#DDDDDD\" disabled";
                                                                                    }
                                                                                    ?>>
                                                <?php
                                                $domain_query = mysqli_query($conn, "select Domain_ID, Doamin_Name from Domain order by Doamin_Name");
                                                while ($domain = mysqli_fetch_array($domain_query, MYSQLI_BOTH)) {
                                                    echo '<option VALUE="' . $domain['Domain_ID'] . '" ' . ($domain['Domain_ID'] == $cus['DDNS_Domain_ID'] ? "SELECTED" : "") . '> ' . $domain['Doamin_Name'] . '</option>';
                                                }
                                                ?>
                                            </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td width="100" height="24" align="right" class="bbody" style="padding-right:5px">Operator contact</td>
                                        <td width="400" class="bbody" style="padding-left:5px; border:solid #999 1px; background-color: lightgray;">
                                            <span id="oprator_contact"><?php echo $oprator_contact ?></span>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table><br />
                    <hr color="#FF3333" /><br />
                </td>
            </tr>
            <tr>
                <td class="bbbody" style="padding-left:15px; padding-right:15px">Connection Info<br /><br />
                    <table border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="100" height="24" align="right" class="bbody" style="padding-right:5px">Product Type</td>
                                        <td><select name="conntype_id" id="conntype_id" <?php
                                                                                        if ($_SESSION['kdb'] < 6) {
                                                                                            echo "style=\"background:#DDDDDD\" disabled";
                                                                                        }
                                                                                        ?>>
                                                <?php
                                                $conntype_query = mysqli_query($conn, "select Connection_ID, Connection_Type from Connection_Type order by Connection_Type");
                                                while ($conntype = mysqli_fetch_array($conntype_query, MYSQLI_BOTH)) {
                                                    echo '<option VALUE="' . $conntype['Connection_ID'] . '" ' . ($conntype['Connection_ID'] == $cus['Connection_Type'] ? "SELECTED" : "") . '> ' . $conntype['Connection_Type'] . '</option>';
                                                }
                                                ?>
                                            </select></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="100" height="24" align="right" class="bbody" style="padding-right:5px">CS No.(Ref)</td>
                                        <td><input name="ref_id" type="text" id="ref_id" value="<?php echo $cus['Ref'] ?>" size="40" <?php
                                                                                                                                        if ($_SESSION['kdb'] < 6 || $_SESSION['kdb'] == 7) {
                                                                                                                                            echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                        }
                                                                                                                                        ?>></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="100" height="24" align="right" class="bbody" style="padding-right:5px">Status</td>
                                        <td><select name="status_id" id="status_id" <?php
                                                                                    if ($_SESSION['kdb'] < 4) {
                                                                                        echo "style=\"background:#DDDDDD\" disabled";
                                                                                    }
                                                                                    ?>>
                                                <?php
                                                $status_query = mysqli_query($conn, "select Status_ID, Status_Name from Status order by Status_Name");
                                                while ($status = mysqli_fetch_array($status_query, MYSQLI_BOTH)) {
                                                    echo '<option VALUE="' . $status['Status_ID'] . '" ' . ($status['Status_ID'] == $cus['Status_ID'] ? "SELECTED" : "") . '> ' . $status['Status_Name'] . '</option>';
                                                }
                                                ?>
                                            </select></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="100" height="24" align="right" class="bbody" style="padding-right:5px">Con Login</td>
                                        <td><input name="login" type="text" id="login" value="<?php echo $cus['Login'] ?>" <?php
                                                                                                                            if ($_SESSION['kdb'] < 6) {
                                                                                                                                echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                            }
                                                                                                                            ?>></td>
                                        <td><select name="domain_id" id="domain_id" <?php
                                                                                    if ($_SESSION['kdb'] < 6) {
                                                                                        echo "style=\"background:#DDDDDD\" disabled";
                                                                                    }
                                                                                    ?>>
                                                <?php
                                                $domain_query = mysqli_query($conn, "select Domain_ID, Doamin_Name from Domain order by Doamin_Name");
                                                while ($domain = mysqli_fetch_array($domain_query, MYSQLI_BOTH)) {
                                                    echo '<option VALUE="' . $domain['Domain_ID'] . '" ' . ($domain['Domain_ID'] == $cus['Domain_ID'] ? "SELECTED" : "") . '> ' . $domain['Doamin_Name'] . '</option>';
                                                }
                                                ?>
                                            </select></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="100" height="24" align="right" class="bbody" style="padding-right:5px">Password</td>
                                        <td><input name="pass" type="text" id="pass" value="<?php echo $cus['Password'] ?>" <?php
                                                                                                                            if ($_SESSION['kdb'] < 6) {
                                                                                                                                echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                            }
                                                                                                                            ?>></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="100" height="24" align="right" class="bbody" style="padding-right:5px">WAN IP</td>
                                        <td><input name="wan_ip" type="text" id="wan_ip" value="<?php echo $cus['WAN_IP'] ?>" maxlength="15" <?php
                                                                                                                                                if ($_SESSION['kdb'] < 6) {
                                                                                                                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                                }
                                                                                                                                                ?>></td>
                                        <td> &nbsp;&nbsp;/ &nbsp;&nbsp;</td>
                                        <td><input name="wan_sub" type="text" id="wan_sub" value="<?php echo $cus['WAN_Sub'] ?>" maxlength="15" <?php
                                                                                                                                                if ($_SESSION['kdb'] < 6) {
                                                                                                                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                                }
                                                                                                                                                ?>></td>

                                    </tr>
                                    <tr>
                                        <td width="100" height="24" align="right" class="bbody" style="padding-right:5px">WAN GW</td>
                                        <td><input name="wan_gw" type="text" id="wan_gw" maxlength="15" value="<?php echo $cus['WAN_GW'] ?>"></td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">MPLS Node</td>
                                        <td width="152">
                                            <input name="MPLS_Node" id="MPLS_Node" type="text" value="<?php echo @$cus['MPLS_Node'] ?>">
                                        </td>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px;padding-left: 5px;">Vlan No.</td>
                                        <td width="152">
                                            <input name="Jinet_Vlan_No" id="Jinet_Vlan_No" type="text" value="<?php echo @$cus['Jinet_Vlan_No'] ?>">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td width="100" height="24" align="right" class="bbody" style="padding-right:5px">ASN</td>
                                        <td><input name="asn" type="text" id="asn" maxlength="15" value="<?php echo $cus['ASN'] ?>"></td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                </table>
                            </td>
                            <td valign="bottom">
                                <table border="0" cellspacing="1" cellpadding="1">
                                    <tr>
                                        <td align="right" class="bbody" style="padding-right:5px">CORP_Interface</td>
                                        <td><input type="text" size="30" id="CORP_Interface" name="CORP_Interface" value="<?php echo $cus['CORP_Interface'] ?>"></td>
                                    </tr>
                                    <tr>
                                        <td align="right" class="bbody" style="padding-right:5px">VSI_Name</td>
                                        <td><input type="text" size="30" id="VSI_Name" name="VSI_Name" value="<?php echo $cus['VSI_Name'] ?>"></td>
                                    </tr>
                                    <tr>
                                        <td align="right" class="bbody" style="padding-right:5px">VPN-Instance</td>
                                        <td><input type="text" size="30" id="vpn_instance" name="vpn_instance" value="<?php echo $cus['vpn_instance'] ?>"></td>
                                    </tr>
                                    <tr>
                                        <td align="right" class="bbody" style="padding-right:5px">Node_ME</td>
                                        <td><input type="text" size="30" id="Node_ME" name="Node_ME" value="<?php echo $cus['Node_ME'] ?>"></td>
                                    </tr>
                                    <tr>
                                        <td align="right" class="bbody" style="padding-right:5px">ME_Interface</td>
                                        <td><input type="text" size="30" id="ME_Interface" name="ME_Interface" value="<?php echo $cus['ME_Interface'] ?>"></td>
                                    </tr>
                                </table>

                                <table border="0" cellspacing="1" cellpadding="0">

                                    <tr>
                                        <td width="50" height="24" align="right" class="bbody" style="padding-right:5px">Link</td>
                                        <td><input name="link" type="text" id="link" value="<?php echo $cus['Link_Login'] ?>" <?php
                                                                                                                                if ($_SESSION['kdb'] < 6) {
                                                                                                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                }
                                                                                                                                ?>></td>
                                        <td><select name="link_domain" id="link_domain" <?php
                                                                                        if ($_SESSION['kdb'] < 6) {
                                                                                            echo "style=\"background:#DDDDDD\" disabled";
                                                                                        }
                                                                                        ?>>
                                                <?php
                                                $domain_query = mysqli_query($conn, "select Domain_ID, Doamin_Name from Domain order by Doamin_Name");
                                                while ($domain = mysqli_fetch_array($domain_query, MYSQLI_BOTH)) {
                                                    echo '<option VALUE="' . $domain['Domain_ID'] . '" ' . ($domain['Domain_ID'] == $cus['Link_Domain_ID'] ? "SELECTED" : "") . '> ' . $domain['Doamin_Name'] . '</option>';
                                                }
                                                ?>
                                            </select></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="50" height="24" align="right" class="bbody" style="padding-right:5px">PW</td>
                                        <td><input name="link_pass" type="text" id="link_pass" value="<?php echo $cus['Link_Password'] ?>" <?php
                                                                                                                                            if ($_SESSION['kdb'] < 6) {
                                                                                                                                                echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                            }
                                                                                                                                            ?>></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="50" height="24" align="right" class="bbody" style="padding-right:5px">LIP</td>
                                        <td><input name="link_ip" type="text" id="link_ip" value="<?php echo $cus['Link_IP'] ?>" maxlength="15" <?php
                                                                                                                                                if ($_SESSION['kdb'] < 6) {
                                                                                                                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                                }
                                                                                                                                                ?>></td>
                                        <td><input name="link_sub" type="text" id="link_sub" value="<?php echo $cus['Link_Sub'] ?>" maxlength="15" <?php
                                                                                                                                                    if ($_SESSION['kdb'] < 6) {
                                                                                                                                                        echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                                    }
                                                                                                                                                    ?>></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="50" height="24" align="right" class="bbody" style="padding-right:5px">T2</td>
                                        <td><input name="tunnel2" type="text" id="tunnel2" value="<?php echo $cus['Tunnel2'] ?>" maxlength="15" <?php
                                                                                                                                                if ($_SESSION['kdb'] < 9) {
                                                                                                                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                                }
                                                                                                                                                ?>></td>
                                        <td><input name="t2sub" type="text" id="t2sub" value="<?php echo $cus['Tunnel2sub'] ?>" maxlength="15" <?php
                                                                                                                                                if ($_SESSION['kdb'] < 9) {
                                                                                                                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                                }
                                                                                                                                                ?>></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="203" height="24">&nbsp;</td>
                                        <td><select name="re_router" id="re_router" <?php
                                                                                    if ($_SESSION['kdb'] < 5) {
                                                                                        echo "style=\"background:#DDDDDD\" disabled";
                                                                                    }
                                                                                    ?>>
                                                <?php
                                                $restart_query = mysqli_query($conn, "select Restart_Router_ID, Restart_Name from Restart order by Restart_Name");
                                                while ($restart = mysqli_fetch_array($restart_query, MYSQLI_BOTH)) {
                                                    echo '<option VALUE="' . $restart['Restart_Router_ID'] . '" ' . ($restart['Restart_Router_ID'] == $cus['Restart_Router'] ? "SELECTED" : "") . '> ' . $restart['Restart_Name'] . '</option>';
                                                }
                                                ?>
                                            </select></td>
                                    </tr>
                                </table>
                            </td>
                            <td valign="bottom">
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="50" height="24" align="right" class="bbody" style="padding-right:5px">Bak</td>
                                        <td><input name="bak_login" type="text" id="bak_login" value="<?php echo $cus['Bak_Login'] ?>" <?php
                                                                                                                                        if ($_SESSION['kdb'] < 6) {
                                                                                                                                            echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                        }
                                                                                                                                        ?>></td>
                                        <td><select name="bak_domain" id="bak_domain" <?php
                                                                                        if ($_SESSION['kdb'] < 6) {
                                                                                            echo "style=\"background:#DDDDDD\" disabled";
                                                                                        }
                                                                                        ?>>
                                                <?php
                                                $domain_query = mysqli_query($conn, "select Domain_ID, Doamin_Name from Domain order by Doamin_Name");
                                                while ($domain = mysqli_fetch_array($domain_query, MYSQLI_BOTH)) {
                                                    echo '<option VALUE="' . $domain['Domain_ID'] . '" ' . ($domain['Domain_ID'] == $cus['Bak_Domain_ID'] ? "SELECTED" : "") . '> ' . $domain['Doamin_Name'] . '</option>';
                                                }
                                                ?>
                                            </select></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="50" height="24" align="right" class="bbody" style="padding-right:5px">PW</td>
                                        <td><input name="bak_pass" type="text" id="bak_pass" value="<?php echo $cus['Bak_Password'] ?>" <?php
                                                                                                                                        if ($_SESSION['kdb'] < 6) {
                                                                                                                                            echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                        }
                                                                                                                                        ?>></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="50" height="24" align="right" class="bbody" style="padding-right:5px">BIP</td>
                                        <td><input name="bak_ip" type="text" id="bak_ip" value="<?php echo $cus['Bak_IP'] ?>" maxlength="15" <?php
                                                                                                                                                if ($_SESSION['kdb'] < 6) {
                                                                                                                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                                }
                                                                                                                                                ?>></td>
                                        <td><input name="bak_sub" type="text" id="bak_sub" value="<?php echo $cus['Bak_Sub'] ?>" maxlength="15" <?php
                                                                                                                                                if ($_SESSION['kdb'] < 6) {
                                                                                                                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                                }
                                                                                                                                                ?>></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="50" height="24" align="right" class="bbody" style="padding-right:5px">T1</td>
                                        <td><input name="tunnel1" type="text" id="tunnel1" value="<?php echo $cus['Tunnel1'] ?>" maxlength="15" <?php
                                                                                                                                                if ($_SESSION['kdb'] < 9) {
                                                                                                                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                                }
                                                                                                                                                ?>></td>
                                        <td><input name="t1sub" type="text" id="t1sub" value="<?php echo $cus['Tunnel1sub'] ?>" maxlength="15" <?php
                                                                                                                                                if ($_SESSION['kdb'] < 9) {
                                                                                                                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                                }
                                                                                                                                                ?>></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="198" height="24" align="right" class="bbody" style="padding-right:5px">Install Date</td>
                                        <td><input name="install_date" type="text" id="install_date" value="<?php echo $cus['Install_Date'] ?>" maxlength="15" <?php
                                                                                                                                                                if ($_SESSION['kdb'] <> 9 && $_SESSION['kdb'] <> 5) {
                                                                                                                                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                                                }
                                                                                                                                                                ?>></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table><br />
                    <hr color="#FF3333" /><br />
                </td>
            </tr>
            <tr>
                <td class="bbbody" style="padding-left:15px; padding-right:15px">EQUIPMENT<br /><br />
                    <table border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td valign="top">
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="80" height="24" align="right" class="bbody" style="padding-right:5px">Equipment</td>
                                        <td><select name="equipment_id" id="equipment_id" <?php
                                                                                            if ($_SESSION['kdb'] < 5) {
                                                                                                echo "style=\"background:#DDDDDD\" disabled";
                                                                                            }
                                                                                            ?>>
                                                <?php
                                                $equipment_query = mysqli_query($conn, "select Equipment_ID, Equipment_Name from Equipment order by Equ_Sort");
                                                while ($equipment = mysqli_fetch_array($equipment_query, MYSQLI_BOTH)) {
                                                    echo '<option VALUE="' . $equipment['Equipment_ID'] . '" ' . ($equipment['Equipment_ID'] == $cus['Equipment_ID'] ? "SELECTED" : "") . '> ' . $equipment['Equipment_Name'] . '</option>';
                                                }
                                                ?>
                                            </select></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="80" height="24" align="right" class="bbody" style="padding-right:5px">Serial #</td>
                                        <td><input name="serial" type="text" id="serial" value="<?php echo $cus['Serial'] ?>" maxlength="50" <?php
                                                                                                                                                if ($_SESSION['kdb'] < 5) {
                                                                                                                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                                }
                                                                                                                                                ?>></td>
                                        <td class="bbody" style="padding-right:5px; padding-left:5px">/</td>
                                        <td><select name="antenna_id" id="antenna_id" <?php
                                                                                        if ($_SESSION['kdb'] < 5) {
                                                                                            echo "style=\"background:#DDDDDD\" disabled";
                                                                                        }
                                                                                        ?>>
                                                <?php
                                                $antenna_query = mysqli_query($conn, "select Antenna_ID, Antenna_Name from Antenna order by Antenna_Name");
                                                while ($antenna = mysqli_fetch_array($antenna_query, MYSQLI_BOTH)) {
                                                    echo '<option VALUE="' . $antenna['Antenna_ID'] . '" ' . ($antenna['Antenna_ID'] == $cus['Antenna'] ? "SELECTED" : "") . '> ' . $antenna['Antenna_Name'] . '</option>';
                                                }
                                                ?>
                                            </select></td>
                                        <td class="bbody" style="padding-right:5px; padding-left:5px">MAC</td>
                                        <td>
                                            <input name="EqMACAddr" id="EqMACAddr" type="text" value="<?= $cus['EqMACAddr'] ?>">
                                        </td>
                                    </tr>
                                </table>

                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="80" height="24" align="right" class="bbody" style="padding-right:5px">UPS</td>
                                        <td><select name="ups_id" id="ups_id" <?php
                                                                                if ($_SESSION['kdb'] < 5 || $_SESSION['kdb'] == 6) {
                                                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                                                }
                                                                                ?>>
                                                <option value="1">-</option>
                                                <?php
                                                $equipment_query = mysqli_query($conn, "select Equipment_ID, Equipment_Name from Equipment where Equ_Type = 'UPS' order by Equipment_Name");
                                                while ($equipment = mysqli_fetch_array($equipment_query, MYSQLI_BOTH)) {
                                                    echo '<option VALUE="' . $equipment['Equipment_ID'] . '" ' . ($equipment['Equipment_ID'] == $cus['UPS_ID'] ? "SELECTED" : "") . '> ' . $equipment['Equipment_Name'] . '</option>';
                                                }
                                                ?>
                                            </select></td>
                                        <td class="bbody" style="padding-right:5px; padding-left:10px">Serial #</td>
                                        <td><input name="ups_serial" type="text" id="ups_serial" value="<?php echo $cus['UPS_Serial'] ?>" <?php
                                                                                                                                            if ($_SESSION['kdb'] < 5 || $_SESSION['kdb'] == 6) {
                                                                                                                                                echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                            }
                                                                                                                                            ?>></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="80" height="24" align="right" class="bbody" style="padding-right:5px">IP LAN1</td>
                                        <td><input name="lan_ip" type="text" id="lan_ip" value="<?php echo $cus['LAN_IP'] ?>" maxlength="15" <?php
                                                                                                                                                if ($_SESSION['kdb'] < 6) {
                                                                                                                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                                }
                                                                                                                                                ?>></td>
                                        <td class="bbody" style="padding-right:5px; padding-left:5px">/</td>
                                        <td><input name="lan_sub" type="text" id="lan_sub" value="<?php echo $cus['LAN_Sub'] ?>" maxlength="15" <?php
                                                                                                                                                if ($_SESSION['kdb'] < 6) {
                                                                                                                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                                }
                                                                                                                                                ?>></td>
                                        <td class="bbody" style="padding-right:5px;padding-left:5px;">IP VIP1</td>
                                        <td width="167">
                                            <input name="IP_VIP1" id="IP_VIP1" type="text" value="<?php echo @$cus['Client_LAN1_VIP1'] ?>">
                                        </td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="80" height="24" align="right" class="bbody" style="padding-right:5px">IP LAN2</td>
                                        <td><input name="lan_ip2" type="text" id="lan_ip2" value="<?php echo $cus['LAN_IP2'] ?>" maxlength="15" <?php
                                                                                                                                                if ($_SESSION['kdb'] < 6) {
                                                                                                                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                                }
                                                                                                                                                ?>></td>
                                        <td class="bbody" style="padding-right:5px; padding-left:5px">/</td>
                                        <td><input name="lan_sub2" type="text" id="lan_sub2" value="<?php echo $cus['LAN_Sub2'] ?>" maxlength="15" <?php
                                                                                                                                                    if ($_SESSION['kdb'] < 6) {
                                                                                                                                                        echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                                    }
                                                                                                                                                    ?>></td>
                                        <td class="bbody" style="padding-right:5px;padding-left:5px;">IP VIP2</td>
                                        <td width="167">
                                            <input name="IP_VIP2" id="IP_VIP2" type="text" value="<?php echo @$cus['Client_LAN2_VIP2'] ?>">
                                        </td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="80" height="24" align="right" class="bbody" style="padding-right:5px">IP LAN3</td>
                                        <td><input name="lan_ip3" type="text" id="lan_ip3" value="<?php echo $cus['LAN_IP3'] ?>" maxlength="15" <?php
                                                                                                                                                if ($_SESSION['kdb'] <> 9 && $_SESSION['kdb'] <> 5) {
                                                                                                                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                                }
                                                                                                                                                ?>></td>
                                        <td class="bbody" style="padding-right:5px; padding-left:5px">/</td>
                                        <td><input name="lan_sub3" type="text" id="lan_sub3" value="<?php echo $cus['LAN_Sub3'] ?>" maxlength="15" <?php
                                                                                                                                                    if ($_SESSION['kdb'] <> 9 && $_SESSION['kdb'] <> 5) {
                                                                                                                                                        echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                                    }
                                                                                                                                                    ?>></td>
                                        <td class="bbody" style="padding-right:5px;padding-left:5px;">IP VIP3</td>
                                        <td width="167">
                                            <input name="IP_VIP3" id="IP_VIP3" type="text" value="<?php echo @$cus['Client_LAN3_VIP3'] ?>">
                                        </td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="80" height="24" align="right" class="bbody" style="padding-right:5px">IP LAN4</td>
                                        <td width="167">
                                            <input name="IP_LAN4" id="IP_LAN4" type="text" value="<?php echo @$cus['Client_LAN4'] ?>">
                                        </td>
                                        <td class="bbody" style="padding-right:5px; padding-left:5px">/</td>
                                        <td width="167">
                                            <input name="IP_LAN4_SUB" id="IP_LAN4_SUB" type="text" value="<?php echo @$cus['Client_LAN4_Subnet'] ?>">
                                        </td>
                                        <td class="bbody" style="padding-right:5px;padding-left:5px;">IP VIP4</td>
                                        <td width="167">
                                            <input name="IP_VIP4" id="IP_VIP4" type="text" value="<?php echo @$cus['Client_LAN4_VIP4'] ?>">
                                        </td>
                                    </tr>
                                </table>


                            </td>
                            <td valign="top">
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <?php
                                        $staff_query = mysqli_query($conn, "select * from Users where UID in ('" . $cus['Profile_By'] . "', '" . $cus['Config_By'] . "')");
                                        $staff = mysqli_fetch_row($staff_query);
                                        ?>
                                        <td width="120" height="24" align="right" class="bbody" style="padding-right:5px">Profile By</td>
                                        <td><select name="profile_by" id="profile_by" <?php
                                                                                        if ($_SESSION['kdb'] <> 9 && $_SESSION['kdb'] <> 5) {
                                                                                            echo "style=\"background:#DDDDDD\" disabled";
                                                                                        }
                                                                                        ?>>
                                                <?php
                                                $staff_query = mysqli_query($conn, "select UID, NickName from Users order by UID");
                                                while ($staff = mysqli_fetch_array($staff_query, MYSQLI_BOTH)) {
                                                    echo '<option VALUE="' . $staff['UID'] . '" ' . ($staff['UID'] == $cus['Profile_By'] ? "SELECTED" : "") . '> ' . $staff['NickName'] . '</option>';
                                                }
                                                ?>
                                            </select></td>
                                        <td align="right" class="bbody" style="padding-right:5px; padding-left:5px">
                                            <input name="conf_date" type="text" id="profile_date" value="<?php echo @$cus['Profile_Date'] ?>" <?php
                                                                                                                                                if ($_SESSION['kdb'] < 5 || $_SESSION['kdb'] == 6) {
                                                                                                                                                    echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                                }
                                                                                                                                                ?>>
                                        </td>
                                        <td></td>
                                        <td align="right" class="bbody" style="padding-right:5px; padding-left:5px"></td>
                                        <td></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <?php
                                        $staff = mysqli_fetch_row($staff_query);
                                        ?>
                                        <td width="120" height="24" align="right" class="bbody" style="padding-right:5px">Config By</td>
                                        <td><select name="config_by" id="config_by" <?php
                                                                                    if ($_SESSION['kdb'] < 5 || $_SESSION['kdb'] == 6) {
                                                                                        echo "style=\"background:#DDDDDD\" disabled";
                                                                                    }
                                                                                    ?>>
                                                <?php
                                                $staff_query = mysqli_query($conn, "select UID, NickName from Users order by UID");
                                                while ($staff = mysqli_fetch_array($staff_query, MYSQLI_BOTH)) {
                                                    echo '<option VALUE="' . $staff['UID'] . '" ' . ($staff['NickName'] == $cus['Config_By'] ? "SELECTED" : "") . '> ' . $staff['NickName'] . '</option>';
                                                }
                                                ?>
                                            </select></td>
                                        <td>
                                            <input name="conf_date" type="text" id="conf_date" value="<?php echo $cus['Config_Date'] ?>" <?php
                                                                                                                                            if ($_SESSION['kdb'] < 5 || $_SESSION['kdb'] == 6) {
                                                                                                                                                echo "style=\"background:#DDDDDD\" disabled";
                                                                                                                                            }
                                                                                                                                            ?>>
                                        </td>
                                    </tr>
                                </table>

                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="120" height="24" align="right" class="bbody" style="padding-right:5px">URL CATCI MON</td>
                                        <td width="350">
                                            <input name="URL_CATCI_MON" id="URL_CATCI_MON" type="text" value="<?php echo @$cus['URL_CATCI_MON'] ?>">
                                        </td>

                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">URL PRTG MON</td>
                                        <td width="350">
                                            <input name="URL_PRTG_MON" id="URL_PRTG_MON" type="text" value="<?php echo @$cus['URL_PRTG_MON'] ?>">
                                        </td>

                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">URL WU MON</td>
                                        <td width="350">
                                            <input name="URL_WU_MON" id="URL_WU_MON" type="text" value="<?php echo @$cus['URL_WU_MON'] ?>">
                                        </td>

                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">User/Pass</td>
                                        <td width="350">
                                            <input name="MON_User_Pass" id="MON_User_Pass" type="text" value="<?php echo @$cus['Mon_User_Pass'] ?>">
                                        </td>

                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">URL Backup Config</td>
                                        <td width="350">
                                            <input name="URL_Backup_Config" id="URL_Backup_Config" type="text" value="<?php echo @$cus['URL_Backup_Config'] ?>">
                                        </td>

                                    </tr>
                                </table>

                            </td>
                        </tr>
                    </table>

                </td>
            </tr>
            <tr>
                <td>
                    <table border="0" cellspacing="1" cellpadding="0">
                        <tr>
                            <td width="80" height="24" align="right" class="bbody" style="padding-right:5px">Equipment#1</td>
                            <td>
                                <input style="width:250px;" name="Equipment1_name" id="Equipment1_name" type="text" value="<?php echo @$cus['Equipment1'] ?>">
                            </td>
                            <td class="bbody" style="padding-right:5px; padding-left:10px">Serial#1</td>
                            <td>
                                <input name="Equipment1_sn" id="Equipment1_sn" type="text" value="<?php echo @$cus['Equipment1_SN'] ?>">
                            </td>
                             <td class="bbody" style="padding-right:5px; padding-left:10px">MAC#1</td>
                            <td>
                                <input name="MACAddr1" id="MACAddr1" type="text" value="<?php echo @$cus['MACAddr1'] ?>">
                            </td>
                        </tr>
                        <tr>
                            <td height="24" align="right" class="bbody" style="padding-right:5px">Equipment#2</td>
                            <td>
                                <input style="width:250px;" name="Equipment2_name" id="Equipment2_name" type="text" value="<?php echo @$cus['Equipment2'] ?>">
                            </td>
                            <td class="bbody" style="padding-right:5px; padding-left:10px">Serial#2</td>
                            <td>
                                <input name="Equipment2_sn" id="Equipment2_sn" type="text" value="<?php echo @$cus['Equipment2_SN'] ?>">
                            </td>
                              <td class="bbody" style="padding-right:5px; padding-left:10px">MAC#2</td>
                            <td>
                                <input name="MACAddr2" id="MACAddr2" type="text" value="<?php echo @$cus['MACAddr2'] ?>">
                            </td>
                        </tr>
                        <tr>
                            <td height="24" align="right" class="bbody" style="padding-right:5px">Equipment#3</td>
                            <td>
                                <input style="width:250px;" name="Equipment3_name" id="Equipment3_name" type="text" value="<?php echo @$cus['Equipment3'] ?>">
                            </td>
                            <td class="bbody" style="padding-right:5px; padding-left:10px">Serial#3</td>
                            <td>
                                <input name="Equipment3_sn" id="Equipment3_sn" type="text" value="<?php echo @$cus['Equipment3_SN'] ?>">
                            </td>
                              <td class="bbody" style="padding-right:5px; padding-left:10px">MAC#3</td>
                            <td>
                                <input name="MACAddr3" id="MACAddr3" type="text" value="<?php echo @$cus['MACAddr3'] ?>">
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td class="bbbody" style="padding-left:15px; padding-right:15px"><br />
                    <table border="0" cellpadding="0" cellspacing="1">
                        <tr>
                            <td width="100" align="right" valign="top" class="bbbody" style="padding-right:5px; padding-top:3px">Remark</td>
                            <td><textarea name="remark" id="remark" cols="174" rows="5" <?php
                                                                                        if ($_SESSION['kdb'] < 3) {
                                                                                            echo "style=\"background:#DDDDDD\" disabled";
                                                                                        }
                                                                                        ?>><?php echo $cus['Remark'] ?></textarea></td>
                        </tr>
                    </table><br />
                    <hr color="#FF3333" /><br />
                </td>
            </tr>
            <tr>
                <td height="48" align="center" valign="top">
                    <input type="submit" name="button" id="button_save" value="Save">
                    <input type="submit" name="button" id="button_calcel" value="Cancel">
                </td>
            </tr>
        </form>
    </table>
    <?php
    //mysqli_close($conn);
    ?>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script type="text/javascript">
        $(document).ready(function() {
            //console.log('jquery loaded');
            $('#op1').change(function() {
                let val = $(this).find(':selected').attr('data-contact');
                console.log(' operator contact: ' + val);
                $('#oprator_contact').text(val);
            });
            // Show the modal
            $("#btnShowPopup").click(function() {
                $("#myModal").show();
            });
            // Close the modal when the user clicks on <span> (x)
            $(".close").click(function() {
                $("#myModal").hide();
            });
            // Close the modal when the user clicks anywhere outside of the modal
            $(window).click(function(event) {
                if ($(event.target).is("#myModal")) {
                    $("#myModal").hide();
                }
            });
            /*
              $('table tbody tr').click(function(){
                    var firstColumnValue = $(this).find('td:first').text();
                    alert("First column value: " + firstColumnValue);
                });
               */
            $("#mod_btn_search").click(function() {
                let str = $("#mod_txtsearch").val();
                if (str.length <= 2) {
                    alert('Please enter > 2 character!');
                    return;
                }
                //alert(str);

                $.ajax({
                    url: 'api_csdb_customers_list.php',
                    type: 'GET',
                    dataType: 'json',
                    data: {
                        opt: 'ajax',
                        cmd: 'search',
                        strsearch: str
                    },
                    success: function(response) {
                        console.log(response);
                        //JSON.stringify(response)
                        $('#mod_results').html(response.data);
                    },
                    error: function(xhr, status, error) {
                        console.error('Error fetching data:', error);
                    }
                });

            });
        });

        function link(l) {
            location.href = l;
        }

        function result_row_selected(domitem) {
            let cusno = $(domitem).find('td:first').text();
            let cusname = $(domitem).find('td:eq(1)').text();
            //alert("First column value: " + firstColumnValue);
            $("#cuscode").val(cusno);
            $("#cusname").text(cusname);
            console.log("cusno:", cusno, " cusname:", cusname);
            $("#myModal").hide();
        }
    </script>

</body>